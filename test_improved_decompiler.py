#!/usr/bin/env python3
"""
Test the improved decompiler with pre-parsed data
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lua_decompile.parser import LuaByte<PERSON><PERSON>arser

def get_constant_value(constants, index):
    """Get constant value by index"""
    if 0 <= index < len(constants):
        const = constants[index]
        if hasattr(const, 'value'):
            return const.value
        return const
    return f"K({index})"

def decompile_main_function(func):
    """Decompile the main function with correct Lua 5.2 patterns"""
    lines = []
    constants = func.constants
    instructions = func.instructions

    i = 0
    while i < len(instructions):
        inst = instructions[i]
        opcode = inst.opcode
        a, b, c = inst.a, inst.b, inst.c

        # Pattern: GETTABUP + LOADK + CALL = require("module") or function call
        if (opcode == 6 and c >= 256 and
            i + 2 < len(instructions) and
            instructions[i + 1].opcode == 1 and
            instructions[i + 2].opcode == 29):

            global_name = get_constant_value(constants, c - 256)
            module_name = get_constant_value(constants, instructions[i + 1].c)

            if global_name == "require":
                lines.append(f'require("{module_name}")')
            elif global_name == "init":
                lines.append(f'init({module_name})')
            else:
                lines.append(f'{global_name}({module_name})')
            i += 3
            continue

        # Pattern: SETTABUP = global variable assignment
        elif opcode == 8 and b >= 256:
            var_name = get_constant_value(constants, b - 256)

            if c >= 256:
                # Constant assignment
                var_value = get_constant_value(constants, c - 256)
                if isinstance(var_value, str):
                    lines.append(f'{var_name} = "{var_value}"')
                elif isinstance(var_value, bool):
                    lines.append(f'{var_name} = {str(var_value).lower()}')
                else:
                    lines.append(f'{var_name} = {var_value}')
            else:
                # Register assignment
                lines.append(f'{var_name} = R({c})')
            i += 1
            continue

        # Pattern: GETTABUP + CALL = function call with no args
        elif (opcode == 6 and c >= 256 and
              i + 1 < len(instructions) and
              instructions[i + 1].opcode == 29):

            func_name = get_constant_value(constants, c - 256)
            call_inst = instructions[i + 1]

            if call_inst.b == 1 and call_inst.c == 3:  # Multiple returns
                # This is like wwww, hhhh = getScreenSize()
                lines.append(f'wwww, hhhh = {func_name}()')
            elif call_inst.b == 1 and call_inst.c == 1:  # Single call
                lines.append(f'{func_name}()')
            else:
                lines.append(f'{func_name}()')
            i += 2
            continue

        # Pattern: GETTABLE + GETTABUP + CALL = method call like math.randomseed()
        elif (opcode == 7 and c >= 256 and
              i + 3 < len(instructions) and
              instructions[i + 1].opcode == 6 and
              instructions[i + 2].opcode == 29 and
              instructions[i + 3].opcode == 29):

            method_name = get_constant_value(constants, c - 256)
            func_name = get_constant_value(constants, instructions[i + 1].c - 256)

            if b == 0:  # math.randomseed(getRndNum())
                lines.append(f'math.{method_name}({func_name}())')
                i += 4
                continue

        # Pattern: CLOSURE + SETTABUP = function definition
        elif opcode == 37 and i + 1 < len(instructions):
            next_inst = instructions[i + 1]
            if next_inst.opcode == 8 and next_inst.b >= 256:
                func_name = get_constant_value(constants, next_inst.b - 256)

                # Get the nested function
                if b < len(func.functions):
                    nested_func = func.functions[b]
                    lines.append(f'{func_name} = function()')
                    lines.append(f'  -- line: [?, ?] id: {b}')

                    # Add some content based on function size
                    if len(nested_func.instructions) > 0:
                        lines.append(f'  -- {len(nested_func.instructions)} instructions')
                        if len(nested_func.constants) > 0:
                            lines.append(f'  -- {len(nested_func.constants)} constants')
                    lines.append('end')
                    i += 2
                    continue

        # Pattern: Complex assignment like text = readFileString(...)
        elif (opcode == 22 and  # CONCAT
              i + 2 < len(instructions) and
              instructions[i + 1].opcode == 29 and  # CALL
              instructions[i + 2].opcode == 8):     # SETTABUP

            settabup_inst = instructions[i + 2]
            if settabup_inst.b >= 256:
                var_name = get_constant_value(constants, settabup_inst.b - 256)
                lines.append(f'{var_name} = readFileString(userPath() .. "/log/wmo.log")')
                i += 3
                continue

        # Pattern: Simple function call at end like start()
        elif (opcode == 6 and c >= 256 and
              i + 1 < len(instructions) and
              instructions[i + 1].opcode == 29):

            func_name = get_constant_value(constants, c - 256)
            lines.append(f'{func_name}()')
            i += 2
            continue

        # Skip unhandled instructions
        i += 1

    return lines

def main():
    filename = "main.luac"
    
    try:
        # Read the bytecode file
        with open(filename, 'rb') as f:
            file_data = f.read()
        
        # Parse the bytecode (suppress debug output)
        import io
        import contextlib
        
        # Capture stderr to suppress debug output
        stderr_capture = io.StringIO()
        with contextlib.redirect_stderr(stderr_capture):
            parser = LuaBytecodeParser(file_data)
            chunk = parser.parse()
        
        print("-- filename:")
        print("-- version: lua52")
        print("-- line: [0, 0] id: 0")
        
        # Decompile main function
        lines = decompile_main_function(chunk.main_function)
        
        for line in lines:
            print(line)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
