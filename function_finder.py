#!/usr/bin/env python3
"""
专门用于查找和分析Lua字节码中所有函数的工具
"""

import struct
import sys

def find_all_functions(data):
    """查找字节码中所有可能的函数"""
    functions = []
    
    # 已知的函数位置和信息
    known_functions = [
        {"name": "main", "start": 29, "instructions": 114, "constants": 56},
        {"name": "SmUI", "start": 1483, "instructions": 1865, "constants": 352},
    ]
    
    print("Known functions:")
    for func in known_functions:
        print(f"  {func['name']}: start={func['start']}, inst={func['instructions']}, const={func['constants']}")
    
    # 计算SmUI函数的大概结束位置
    smui_start = 1483
    smui_inst_size = 1865 * 4  # 每条指令4字节
    smui_const_size = 352 * 20  # 每个常量估计20字节
    smui_end = smui_start + 100 + smui_inst_size + smui_const_size  # 加上头部信息
    
    print(f"\nEstimated SmUI function end: {smui_end}")
    print(f"Remaining data after SmUI: {len(data) - smui_end} bytes")
    
    # 在SmUI函数之后查找其他函数
    search_start = smui_end
    if search_start < len(data):
        remaining_data = data[search_start:]
        print(f"\nSearching for additional functions in remaining {len(remaining_data)} bytes...")
        
        # 查找可能的函数开始模式
        for i in range(0, len(remaining_data) - 20, 4):
            pos = search_start + i
            
            # 检查是否是函数开始的模式
            if i + 16 < len(remaining_data):
                chunk = remaining_data[i:i+16]
                
                # 模式1: 源文件名长度为0，然后是行号信息
                if chunk[0:4] == b'\x00\x00\x00\x00':  # 源文件名长度为0
                    try:
                        line_start = struct.unpack('<I', chunk[4:8])[0]
                        line_end = struct.unpack('<I', chunk[8:12])[0]
                        params_info = struct.unpack('<I', chunk[12:16])[0]
                        
                        # 检查是否是合理的行号
                        if 0 < line_start < 10000 and line_start <= line_end < 10000:
                            print(f"Potential function at {pos}: lines {line_start}-{line_end}")
                            
                            # 尝试读取指令数量
                            if i + 20 < len(remaining_data):
                                inst_count_bytes = remaining_data[i+16:i+20]
                                inst_count = struct.unpack('<I', inst_count_bytes)[0]
                                if 1 <= inst_count <= 5000:
                                    print(f"  -> Instructions: {inst_count}")
                                    
                                    functions.append({
                                        "position": pos,
                                        "line_start": line_start,
                                        "line_end": line_end,
                                        "instructions": inst_count
                                    })
                    except:
                        pass
    
    return functions

def analyze_function_at_position(data, pos):
    """分析指定位置的函数"""
    if pos + 50 > len(data):
        return None
    
    try:
        # 读取函数头部信息
        source_len = struct.unpack('<I', data[pos:pos+4])[0]
        pos += 4
        
        source = ""
        if source_len > 0 and pos + source_len < len(data):
            source = data[pos:pos+source_len].decode('utf-8', errors='ignore')
            pos += source_len
        
        line_start = struct.unpack('<I', data[pos:pos+4])[0]
        line_end = struct.unpack('<I', data[pos+4:pos+8])[0]
        pos += 8
        
        params_info = struct.unpack('<I', data[pos:pos+4])[0]
        pos += 4
        
        inst_count = struct.unpack('<I', data[pos:pos+4])[0]
        pos += 4
        
        return {
            "source": source,
            "line_start": line_start,
            "line_end": line_end,
            "instructions": inst_count,
            "data_start": pos
        }
    except:
        return None

def main():
    with open('main.luac', 'rb') as f:
        data = f.read()
    
    print(f"Analyzing Lua bytecode file: {len(data)} bytes")
    
    # 查找所有函数
    functions = find_all_functions(data)
    
    print(f"\nFound {len(functions)} potential additional functions:")
    for i, func in enumerate(functions):
        print(f"Function {i+3}: pos={func['position']}, lines={func['line_start']}-{func['line_end']}, inst={func['instructions']}")
        
        # 尝试分析这个函数的详细信息
        detail = analyze_function_at_position(data, func['position'])
        if detail:
            print(f"  -> Source: '{detail['source']}'")
            print(f"  -> Instructions: {detail['instructions']}")

if __name__ == "__main__":
    main()
