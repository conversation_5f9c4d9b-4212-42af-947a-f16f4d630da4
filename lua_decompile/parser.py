"""
Lua bytecode parser
"""

import struct
from typing import BinaryIO, List, Union
from .lua_types import *
from .constants import *


class LuaBytecodeParser:
    """Parser for Lua bytecode files"""
    
    def __init__(self, file_data: bytes):
        self.data = file_data
        self.pos = 0
        self.header = LuaHeader()
    
    def read_byte(self) -> int:
        """Read a single byte"""
        if self.pos >= len(self.data):
            raise EOFError("Unexpected end of file")
        value = self.data[self.pos]
        self.pos += 1
        return value
    
    def read_bytes(self, count: int) -> bytes:
        """Read multiple bytes"""
        if self.pos + count > len(self.data):
            raise EOFError("Unexpected end of file")
        value = self.data[self.pos:self.pos + count]
        self.pos += count
        return value
    
    def read_int(self) -> int:
        """Read a 4-byte integer"""
        data = self.read_bytes(4)
        return struct.unpack('<I', data)[0]
    
    def read_size_t(self) -> int:
        """Read a size_t value"""
        if self.header.size_t_size == 4:
            return self.read_int()
        elif self.header.size_t_size == 8:
            data = self.read_bytes(8)
            return struct.unpack('<Q', data)[0]
        else:
            raise ValueError(f"Unsupported size_t size: {self.header.size_t_size}")
    
    def read_number(self) -> Union[int, float]:
        """Read a Lua number"""
        if self.header.lua_number_size == 8:
            data = self.read_bytes(8)
            if self.header.integral_flag:
                return struct.unpack('<q', data)[0]  # signed long long
            else:
                return struct.unpack('<d', data)[0]  # double
        elif self.header.lua_number_size == 4:
            data = self.read_bytes(4)
            if self.header.integral_flag:
                return struct.unpack('<i', data)[0]  # signed int
            else:
                return struct.unpack('<f', data)[0]  # float
        else:
            raise ValueError(f"Unsupported number size: {self.header.lua_number_size}")
    
    def read_string(self) -> str:
        """Read a Lua string"""
        size = self.read_size_t()

        if size == 0:
            return ""

        # Size includes null terminator
        if size > len(self.data) - self.pos:
            return ""

        string_data = self.read_bytes(size - 1)
        null_byte = self.read_byte()  # Read null terminator

        try:
            return string_data.decode('utf-8')
        except UnicodeDecodeError:
            return string_data.decode('latin-1')
    
    def parse_header(self) -> LuaHeader:
        """Parse the Lua bytecode header"""
        # Check signature
        signature = self.read_bytes(4)
        if signature not in [LUA_SIGNATURE, LUA_SIGNATURE_52]:
            if isinstance(signature, bytes):
                sig_hex = signature.hex()
            else:
                sig_hex = str(signature)
            raise ValueError(f"Invalid Lua signature: {sig_hex}")

        # Read version
        version_byte = self.read_byte()
        try:
            self.header.version = LuaVersion(version_byte)
        except ValueError:
            raise ValueError(f"Unsupported Lua version: 0x{version_byte:02x}")

        # Read format version
        self.header.format = self.read_byte()

        # Read endianness
        self.header.endianness = self.read_byte()

        # Read sizes
        self.header.int_size = self.read_byte()
        self.header.size_t_size = self.read_byte()
        self.header.instruction_size = self.read_byte()
        self.header.lua_number_size = self.read_byte()
        self.header.integral_flag = self.read_byte()

        # For Lua 5.2+, there's additional header data
        if self.header.version in [LuaVersion.LUA_52, LuaVersion.LUA_53]:
            # Skip the tail data (usually 6 bytes: 0x19, 0x93, 0x0d, 0x0a, 0x1a, 0x0a)
            tail_data = self.read_bytes(6)

        return self.header
    
    def parse_constant(self) -> LuaValue:
        """Parse a single constant"""
        const_type = self.read_byte()

        if const_type == LUA_TNIL:
            return LuaNil()
        elif const_type == LUA_TBOOLEAN:
            value = self.read_byte() != 0
            return LuaBoolean(value)
        elif const_type == LUA_TNUMBER:
            value = self.read_number()
            return LuaNumber(value)
        elif const_type == LUA_TSTRING:
            value = self.read_string()
            return LuaString(value)
        elif const_type == LUA_TTHREAD:
            # Thread type - skip for now, return nil
            print(f"Warning: Thread constant type not fully supported, treating as nil")
            return LuaNil()
        elif const_type == LUA_TTABLE:
            # Table type - skip for now, return nil
            print(f"Warning: Table constant type not fully supported, treating as nil")
            return LuaNil()
        elif const_type == LUA_TFUNCTION:
            # Function type - skip for now, return nil
            print(f"Warning: Function constant type not fully supported, treating as nil")
            return LuaNil()
        elif const_type == LUA_TUSERDATA:
            # Userdata type - skip for now, return nil
            print(f"Warning: Userdata constant type not fully supported, treating as nil")
            return LuaNil()
        elif const_type == LUA_TLIGHTUSERDATA:
            # Light userdata type - skip for now, return nil
            print(f"Warning: Light userdata constant type not fully supported, treating as nil")
            return LuaNil()
        else:
            print(f"Warning: Unknown constant type {const_type}, treating as nil")
            return LuaNil()
    
    def parse_constants(self) -> List[LuaValue]:
        """Parse the constants table"""
        count = self.read_int()
        constants = []
        
        for _ in range(count):
            constant = self.parse_constant()
            constants.append(constant)
        
        return constants
    
    def parse_instructions(self) -> List[Instruction]:
        """Parse the instruction list"""
        count = self.read_int()
        instructions = []
        
        for _ in range(count):
            instruction_data = self.read_int()
            instruction = Instruction(instruction_data)
            instructions.append(instruction)
        
        return instructions

    def find_constants_start(self) -> int:
        """Find the start position of constants table"""
        # Based on debug analysis, constants start around position 490
        # Let's look for the constants count (56) followed by the first string "require"

        # Try position 490 (just before "require" at 494)
        test_pos = 490
        if test_pos + 4 <= len(self.data):
            try:
                count = struct.unpack('<I', self.data[test_pos:test_pos+4])[0]
                if count == 56:  # We know there are 56 constants
                    print(f"Found constants start at position {test_pos} with count {count}")
                    return test_pos
            except:
                pass

        # If that doesn't work, search more broadly
        for test_pos in range(480, 500):
            if test_pos + 4 <= len(self.data):
                try:
                    count = struct.unpack('<I', self.data[test_pos:test_pos+4])[0]
                    if count == 56:
                        print(f"Found constants start at position {test_pos} with count {count}")
                        return test_pos
                except:
                    continue

        return -1

    def parse_simple_function(self) -> LuaFunction:
        """Parse a nested function with simpler logic"""
        function = LuaFunction()
        start_pos = self.pos

        try:
            # Check if we have enough data for basic function structure
            if self.pos + 20 > len(self.data):
                print(f"Not enough data for function at position {self.pos}")
                return function

            # Read source string length and skip it
            source_len = self.read_int()
            if source_len > 0 and source_len < 1000:  # Reasonable limit
                if self.pos + source_len <= len(self.data):
                    self.pos += source_len
                else:
                    print(f"Source string too long: {source_len}")
                    return function

            # Read line info
            function.line_defined = self.read_int()
            function.last_line_defined = self.read_int()
            function.num_upvalues = self.read_byte()
            function.num_params = self.read_byte()
            function.is_vararg = self.read_byte() != 0
            function.max_stack_size = self.read_byte()

            # Parse instructions
            inst_count = self.read_int()
            print(f"  Function has {inst_count} instructions")
            if 0 <= inst_count <= 1000:  # Reasonable limit
                for i in range(inst_count):
                    if self.pos + 4 <= len(self.data):
                        instruction_data = self.read_int()
                        instruction = Instruction(instruction_data)
                        function.instructions.append(instruction)
                    else:
                        print(f"  Stopped at instruction {i} due to EOF")
                        break
            else:
                print(f"  Unreasonable instruction count: {inst_count}")

            # Parse constants
            const_count = self.read_int()
            print(f"  Function has {const_count} constants")
            if 0 <= const_count <= 100:  # Reasonable limit
                for i in range(const_count):
                    if self.pos >= len(self.data) - 10:
                        print(f"  Stopped at constant {i} due to EOF")
                        break
                    try:
                        constant = self.parse_constant()
                        function.constants.append(constant)
                    except Exception as e:
                        print(f"  Error parsing constant {i}: {e}")
                        break
            else:
                print(f"  Unreasonable constant count: {const_count}")

            # Parse nested functions count (but don't parse them to avoid deep recursion)
            if self.pos + 4 <= len(self.data):
                nested_count = self.read_int()
                print(f"  Function has {nested_count} nested functions (skipping)")
                # For now, just skip nested functions to avoid complexity
                function.functions = []

            print(f"  Successfully parsed function: {len(function.instructions)} instructions, {len(function.constants)} constants")

        except Exception as e:
            print(f"Error parsing function at position {start_pos}: {e}")
            # Return a minimal function
            function.instructions = []
            function.constants = []
            function.functions = []

        return function

    def parse_nested_function(self) -> LuaFunction:
        """Parse a nested function with proper Lua 5.2 format handling"""
        function = LuaFunction()
        start_pos = self.pos

        try:
            print(f"  Starting nested function parse at position {self.pos}")

            # Read source string (function name/source)
            source_len = self.read_int()
            print(f"  Source length: {source_len}")

            if source_len > 0:
                if source_len > 1000:  # Sanity check
                    print(f"  Source length too large: {source_len}")
                    return None
                if self.pos + source_len <= len(self.data):
                    source_data = self.data[self.pos:self.pos + source_len - 1]  # Exclude null terminator
                    self.pos += source_len
                    try:
                        function.source = source_data.decode('utf-8')
                        print(f"  Source: '{function.source}'")
                    except:
                        function.source = ""
                else:
                    print(f"  Not enough data for source string")
                    return None

            # Read line info
            function.line_defined = self.read_int()
            function.last_line_defined = self.read_int()
            print(f"  Lines: {function.line_defined}-{function.last_line_defined}")

            # Read function parameters
            function.num_upvalues = self.read_byte()
            function.num_params = self.read_byte()
            function.is_vararg = self.read_byte() != 0
            function.max_stack_size = self.read_byte()
            print(f"  Params: upvalues={function.num_upvalues}, params={function.num_params}, vararg={function.is_vararg}, stack={function.max_stack_size}")

            # Parse instructions
            inst_count = self.read_int()
            print(f"  Instructions count: {inst_count}")

            if 0 <= inst_count <= 10000:  # Reasonable limit
                for i in range(inst_count):
                    if self.pos + 4 <= len(self.data):
                        instruction_data = self.read_int()
                        instruction = Instruction(instruction_data)
                        function.instructions.append(instruction)
                    else:
                        print(f"  Stopped at instruction {i} due to EOF")
                        break
                print(f"  Parsed {len(function.instructions)} instructions")
            else:
                print(f"  Unreasonable instruction count: {inst_count}")
                return None

            # Parse constants
            const_count = self.read_int()
            print(f"  Constants count: {const_count}")

            if 0 <= const_count <= 1000:  # Reasonable limit
                for i in range(const_count):
                    if self.pos >= len(self.data) - 5:
                        print(f"  Stopped at constant {i} due to EOF")
                        break
                    try:
                        constant = self.parse_constant()
                        function.constants.append(constant)
                    except Exception as e:
                        print(f"  Error parsing constant {i}: {e}")
                        break
                print(f"  Parsed {len(function.constants)} constants")
            else:
                print(f"  Unreasonable constant count: {const_count}")
                # Try to continue anyway
                function.constants = []

            # Parse nested functions (recursive)
            if self.pos + 4 <= len(self.data):
                nested_count = self.read_int()
                print(f"  Nested functions count: {nested_count}")

                if 0 <= nested_count <= 5:  # Conservative limit for nested functions
                    for i in range(nested_count):
                        try:
                            nested_func = self.parse_nested_function()
                            if nested_func:
                                function.functions.append(nested_func)
                        except Exception as e:
                            print(f"  Error parsing nested function {i}: {e}")
                            break
                else:
                    print(f"  Too many nested functions: {nested_count}")
                    function.functions = []

            # Skip debug info for now
            print(f"  Successfully parsed nested function")
            return function

        except Exception as e:
            print(f"  Error parsing nested function at position {start_pos}: {e}")
            return None

    def parse_locals(self) -> List[LuaLocal]:
        """Parse local variable information"""
        count = self.read_int()
        locals_list = []
        
        for _ in range(count):
            name = self.read_string()
            start_pc = self.read_int()
            end_pc = self.read_int()
            local_var = LuaLocal(name, start_pc, end_pc)
            locals_list.append(local_var)
        
        return locals_list
    
    def parse_upvalues(self) -> List[LuaUpvalue]:
        """Parse upvalue information"""
        count = self.read_int()
        upvalues = []
        
        for _ in range(count):
            name = self.read_string()
            upvalue = LuaUpvalue(name, False, 0)  # Simplified for now
            upvalues.append(upvalue)
        
        return upvalues
    
    def parse_function(self) -> LuaFunction:
        """Parse a Lua function"""
        function = LuaFunction()

        try:
            # For Lua 5.2, let's try a different approach
            # Skip to where we know the instructions are (based on our analysis)

            # First, try to find the instruction count
            start_pos = self.pos
            inst_pos = None
            inst_count = 0

            # Look for instruction patterns in the next 50 bytes
            for offset in range(0, 50):
                test_pos = start_pos + offset
                if test_pos + 4 > len(self.data):
                    continue

                saved_pos = self.pos
                self.pos = test_pos

                try:
                    potential_count = self.read_int()
                    if 1 <= potential_count <= 10000:
                        # Check if following data looks like instructions
                        valid = True
                        for i in range(min(3, potential_count)):
                            if self.pos + 4 > len(self.data):
                                valid = False
                                break
                            inst = self.read_int()
                            opcode = inst & 0x3F
                            if not (0 <= opcode <= 37):
                                valid = False
                                break

                        if valid:
                            inst_pos = test_pos
                            inst_count = potential_count
                            break
                except:
                    pass
                finally:
                    self.pos = saved_pos

            if inst_pos is None:
                print("Could not find instruction start, using default parsing")
                # Fall back to default parsing
                function.source = self.read_string()
                function.line_defined = self.read_int()
                function.last_line_defined = self.read_int()
                function.num_upvalues = self.read_byte()
                function.num_params = self.read_byte()
                function.is_vararg = self.read_byte() != 0
                function.max_stack_size = self.read_byte()
                function.instructions = self.parse_instructions()
            else:
                print(f"Found instructions at position {inst_pos}, count: {inst_count}")

                # Parse instructions first since we found them
                self.pos = inst_pos
                function.instructions = self.parse_instructions()

                # Set some default values for now
                function.source = ""
                function.line_defined = 0
                function.last_line_defined = 0
                function.num_upvalues = 0
                function.num_params = 0
                function.is_vararg = False
                function.max_stack_size = 2  # Reasonable default

            # Parse constants from known location (based on debug analysis)
            try:
                # Constants start around position 494 based on our analysis
                const_start_pos = self.find_constants_start()
                if const_start_pos > 0:
                    saved_pos = self.pos
                    self.pos = const_start_pos
                    function.constants = self.parse_constants()
                    print(f"Successfully parsed {len(function.constants)} constants")
                    # Update position to after constants for nested function parsing
                    self.pos = self.pos  # Keep current position after constants
                else:
                    print("Could not find constants start position")
                    function.constants = []
            except Exception as e:
                print(f"Could not parse constants: {e}")
                print(f"Position: {self.pos}")
                function.constants = []

            # Parse nested functions - they contain important logic
            try:
                if self.pos + 4 <= len(self.data):
                    func_count = self.read_int()
                    print(f"Found {func_count} nested functions at position {self.pos-4}")

                    if 0 <= func_count <= 20:  # Reasonable limit
                        for i in range(func_count):
                            print(f"Parsing nested function {i+1}/{func_count}")

                            # Check if we have enough data left
                            if self.pos >= len(self.data) - 20:
                                print(f"Not enough data left for function {i+1}, stopping")
                                break

                            try:
                                # Use the main parse_function method but with better error handling
                                saved_pos = self.pos
                                nested_func = self.parse_nested_function()
                                if nested_func:
                                    function.functions.append(nested_func)
                                    print(f"Successfully parsed nested function {i+1}: {len(nested_func.instructions)} instructions")
                                else:
                                    print(f"Failed to parse nested function {i+1}")
                            except Exception as e:
                                print(f"Error parsing nested function {i+1}: {e}")
                                # Try to recover by skipping ahead
                                self.pos = saved_pos + 100  # Skip ahead
                                continue
                    else:
                        print(f"Too many nested functions ({func_count}), likely parsing error")
                        function.functions = []
                else:
                    print("Not enough data to read function count")
                    function.functions = []
            except Exception as e:
                print(f"Error reading nested function count: {e}")
                function.functions = []

            # Try to parse debug info
            try:
                line_count = self.read_int()
                if 0 <= line_count <= len(function.instructions) * 2:  # Reasonable limit
                    for _ in range(line_count):
                        line = self.read_int()
                        function.line_info.append(line)
            except:
                print("Could not parse line info")
                function.line_info = []

            # Try to parse locals and upvalues
            try:
                function.locals = self.parse_locals()
            except:
                function.locals = []

            try:
                if self.header.version in [LuaVersion.LUA_52, LuaVersion.LUA_53]:
                    upvalue_count = self.read_int()
                    if 0 <= upvalue_count <= 100:
                        for _ in range(upvalue_count):
                            name = self.read_string()
                            upvalue = LuaUpvalue(name, False, 0)
                            function.upvalues.append(upvalue)
                else:
                    function.upvalues = self.parse_upvalues()
            except:
                function.upvalues = []

        except Exception as e:
            print(f"Error parsing function: {e}")
            print(f"Position when error occurred: {self.pos}")

        return function
    
    def find_function_start(self) -> int:
        """Find the actual start of the function data"""
        start_pos = self.pos

        # For Lua 5.2, we need to find where the instructions start
        # and work backwards to find the function header

        # Look for instruction patterns
        for offset in range(0, 50):
            test_pos = start_pos + offset
            if test_pos + 4 > len(self.data):
                continue

            # Save current position
            saved_pos = self.pos
            self.pos = test_pos

            try:
                inst_count = self.read_int()
                if 1 <= inst_count <= 10000:  # Reasonable instruction count
                    # Check if the following data looks like instructions
                    valid_instructions = 0
                    for i in range(min(3, inst_count)):
                        if self.pos + 4 > len(self.data):
                            break
                        inst = self.read_int()
                        opcode = inst & 0x3F
                        if 0 <= opcode <= 37:  # Valid opcode range
                            valid_instructions += 1
                        else:
                            break

                    if valid_instructions >= min(3, inst_count):
                        # Found instructions! Now find the function header
                        # For now, assume the function starts at the beginning
                        # We'll improve this later
                        self.pos = start_pos
                        return start_pos
            except:
                pass
            finally:
                self.pos = saved_pos

        # If we can't find instructions, assume function starts at current position
        return start_pos

    def parse(self) -> LuaChunk:
        """Parse the entire Lua chunk"""
        chunk = LuaChunk()

        # Parse header
        chunk.header = self.parse_header()

        # Find the actual function start
        function_start = self.find_function_start()
        self.pos = function_start

        # Parse main function
        chunk.main_function = self.parse_function()

        return chunk


def parse_lua_bytecode(file_path: str) -> LuaChunk:
    """Parse a Lua bytecode file"""
    with open(file_path, 'rb') as f:
        data = f.read()
    
    parser = LuaBytecodeParser(data)
    return parser.parse()
