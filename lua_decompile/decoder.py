"""
Lua instruction decoder
"""

from typing import Dict, <PERSON>, Tuple, Optional
from .lua_types import Instruction, LuaFunction, LuaValue
from .constants import OpCode, OPCODE_INFO, InstructionMode


class InstructionDecoder:
    """Decoder for Lua instructions"""
    
    def __init__(self, function: LuaFunction):
        self.function = function
        self.instructions = function.instructions
        self.constants = function.constants
    
    def get_opcode_name(self, opcode: int) -> str:
        """Get the name of an opcode"""
        if opcode in OPCODE_INFO:
            return OPCODE_INFO[opcode][0]
        return f"UNKNOWN_{opcode}"
    
    def get_constant(self, index: int) -> LuaValue:
        """Get a constant by index"""
        if 0 <= index < len(self.constants):
            return self.constants[index]
        raise IndexError(f"Constant index {index} out of range")
    
    def is_constant(self, value: int) -> bool:
        """Check if a value refers to a constant"""
        return value >= 256
    
    def get_rk_value(self, rk: int) -> str:
        """Get RK value (register or constant)"""
        if self.is_constant(rk):
            # It's a constant
            const_index = rk - 256
            if 0 <= const_index < len(self.constants):
                constant = self.constants[const_index]
                return str(constant)
            return f"K({const_index})"
        else:
            # It's a register
            return f"R({rk})"
    
    def decode_instruction(self, instruction: Instruction) -> str:
        """Decode a single instruction to human-readable format"""
        opcode = instruction.opcode
        opname = self.get_opcode_name(opcode)
        
        if opcode not in OPCODE_INFO:
            return f"{opname} {instruction.a} {instruction.b} {instruction.c}"
        
        mode = OPCODE_INFO[opcode][1]
        
        if mode == InstructionMode.iABC:
            return self._decode_abc(opname, instruction)
        elif mode == InstructionMode.iABx:
            return self._decode_abx(opname, instruction)
        elif mode == InstructionMode.iAsBx:
            return self._decode_asbx(opname, instruction)
        
        return f"{opname} {instruction.a} {instruction.b} {instruction.c}"
    
    def _decode_abc(self, opname: str, inst: Instruction) -> str:
        """Decode ABC format instruction"""
        if opname == "MOVE":
            return f"R({inst.a}) := R({inst.b})"
        elif opname == "LOADBOOL":
            value = "true" if inst.b else "false"
            result = f"R({inst.a}) := {value}"
            if inst.c:
                result += "; pc++"
            return result
        elif opname == "LOADNIL":
            if inst.b == 0:
                return f"R({inst.a}) := nil"
            else:
                return f"R({inst.a}) to R({inst.a + inst.b}) := nil"
        elif opname == "GETUPVAL":
            return f"R({inst.a}) := U({inst.b})"
        elif opname == "GETTABLE":
            return f"R({inst.a}) := R({inst.b})[{self.get_rk_value(inst.c)}]"
        elif opname == "SETUPVAL":
            return f"U({inst.b}) := R({inst.a})"
        elif opname == "SETTABLE":
            return f"R({inst.a})[{self.get_rk_value(inst.b)}] := {self.get_rk_value(inst.c)}"
        elif opname == "NEWTABLE":
            return f"R({inst.a}) := {{}} (array={inst.b}, hash={inst.c})"
        elif opname == "SELF":
            return f"R({inst.a+1}) := R({inst.b}); R({inst.a}) := R({inst.b})[{self.get_rk_value(inst.c)}]"
        elif opname in ["ADD", "SUB", "MUL", "DIV", "MOD", "POW"]:
            op_map = {"ADD": "+", "SUB": "-", "MUL": "*", "DIV": "/", "MOD": "%", "POW": "^"}
            op = op_map[opname]
            return f"R({inst.a}) := {self.get_rk_value(inst.b)} {op} {self.get_rk_value(inst.c)}"
        elif opname in ["UNM", "NOT", "LEN"]:
            op_map = {"UNM": "-", "NOT": "not ", "LEN": "#"}
            op = op_map[opname]
            return f"R({inst.a}) := {op}R({inst.b})"
        elif opname == "CONCAT":
            return f"R({inst.a}) := R({inst.b}) .. R({inst.b+1}) .. ... .. R({inst.c})"
        elif opname in ["EQ", "LT", "LE"]:
            op_map = {"EQ": "==", "LT": "<", "LE": "<="}
            op = op_map[opname]
            cond = "if" if inst.a else "if not"
            return f"{cond} {self.get_rk_value(inst.b)} {op} {self.get_rk_value(inst.c)} then pc++"
        elif opname == "TEST":
            cond = "if" if inst.c else "if not"
            return f"{cond} R({inst.a}) then pc++"
        elif opname == "TESTSET":
            cond = "if" if inst.c else "if not"
            return f"{cond} R({inst.b}) then R({inst.a}) := R({inst.b}) else pc++"
        elif opname == "CALL":
            if inst.b == 0:
                args = "top"
            elif inst.b == 1:
                args = "0"
            else:
                args = f"{inst.b-1}"
            
            if inst.c == 0:
                returns = "top"
            elif inst.c == 1:
                returns = "0"
            else:
                returns = f"{inst.c-1}"
            
            return f"R({inst.a}), ... ,R({inst.a}+{returns}-1) := R({inst.a})(R({inst.a}+1), ... ,R({inst.a})+{args})"
        elif opname == "TAILCALL":
            if inst.b == 0:
                args = "top"
            else:
                args = f"{inst.b-1}"
            return f"return R({inst.a})(R({inst.a}+1), ... ,R({inst.a})+{args})"
        elif opname == "RETURN":
            if inst.b == 0:
                return "return R(A), ... ,R(top)"
            elif inst.b == 1:
                return "return"
            else:
                return f"return R({inst.a}), ... ,R({inst.a}+{inst.b-2})"
        elif opname == "TFORLOOP":
            return f"R({inst.a}+3), ... ,R({inst.a}+2+{inst.c}) := R({inst.a})(R({inst.a}+1), R({inst.a}+2)); if R({inst.a}+3) ~= nil then R({inst.a}+2)=R({inst.a}+3) else pc++"
        elif opname == "SETLIST":
            if inst.c == 0:
                return f"R({inst.a})[{(inst.b-1)*50+1}] := R({inst.a}+1), ... ,R({inst.a})+top"
            else:
                return f"R({inst.a})[{(inst.b-1)*50+1}] := R({inst.a}+1), ... ,R({inst.a}+{inst.c})"
        elif opname == "CLOSE":
            return f"close all variables in the stack up to (>=) R({inst.a})"
        elif opname == "VARARG":
            if inst.b == 0:
                return f"R({inst.a}), R({inst.a}+1), ..., R({inst.a}+top) := vararg"
            else:
                return f"R({inst.a}), R({inst.a}+1), ..., R({inst.a}+{inst.b-2}) := vararg"
        
        return f"{opname} {inst.a} {inst.b} {inst.c}"
    
    def _decode_abx(self, opname: str, inst: Instruction) -> str:
        """Decode ABx format instruction"""
        if opname == "LOADK":
            if inst.bx < len(self.constants):
                constant = self.constants[inst.bx]
                return f"R({inst.a}) := {constant}"
            return f"R({inst.a}) := K({inst.bx})"
        elif opname == "GETGLOBAL":
            if inst.bx < len(self.constants):
                constant = self.constants[inst.bx]
                return f"R({inst.a}) := _G[{constant}]"
            return f"R({inst.a}) := _G[K({inst.bx})]"
        elif opname == "SETGLOBAL":
            if inst.bx < len(self.constants):
                constant = self.constants[inst.bx]
                return f"_G[{constant}] := R({inst.a})"
            return f"_G[K({inst.bx})] := R({inst.a})"
        elif opname == "CLOSURE":
            return f"R({inst.a}) := closure(KPROTO[{inst.bx}])"
        
        return f"{opname} {inst.a} {inst.bx}"
    
    def _decode_asbx(self, opname: str, inst: Instruction) -> str:
        """Decode AsBx format instruction"""
        if opname == "JMP":
            return f"pc += {inst.sbx}"
        elif opname == "FORLOOP":
            return f"R({inst.a}) += R({inst.a}+2); if R({inst.a}) <?= R({inst.a}+1) then {{ pc += {inst.sbx}; R({inst.a}+3) = R({inst.a}) }}"
        elif opname == "FORPREP":
            return f"R({inst.a}) -= R({inst.a}+2); pc += {inst.sbx}"
        
        return f"{opname} {inst.a} {inst.sbx}"
    
    def decode_all_instructions(self) -> List[str]:
        """Decode all instructions in the function"""
        decoded = []
        for i, instruction in enumerate(self.instructions):
            line_info = ""
            if i < len(self.function.line_info):
                line_info = f"[{self.function.line_info[i]}] "
            
            decoded_inst = self.decode_instruction(instruction)
            decoded.append(f"{i+1:4d}: {line_info}{decoded_inst}")
        
        return decoded
