"""
Main entry point for Lua decompiler
"""

import sys
import argparse
import traceback
from pathlib import Path

from .parser import parse_lua_bytecode
from .decompiler import <PERSON><PERSON>D<PERSON>ompiler
from .decoder import InstructionDecoder
from .simple_decompiler import simple_decompile


def print_chunk_info(chunk):
    """Print information about the Lua chunk"""
    print(f"Lua version: {chunk.header.version.name}")
    print(f"Format: {chunk.header.format}")
    print(f"Endianness: {chunk.header.endianness}")
    print(f"Int size: {chunk.header.int_size}")
    print(f"Size_t size: {chunk.header.size_t_size}")
    print(f"Instruction size: {chunk.header.instruction_size}")
    print(f"Number size: {chunk.header.lua_number_size}")
    print(f"Integral flag: {chunk.header.integral_flag}")
    print()
    
    func = chunk.main_function
    print(f"Main function:")
    print(f"  Source: {func.source}")
    print(f"  Line defined: {func.line_defined}")
    print(f"  Last line defined: {func.last_line_defined}")
    print(f"  Number of upvalues: {func.num_upvalues}")
    print(f"  Number of parameters: {func.num_params}")
    print(f"  Is vararg: {func.is_vararg}")
    print(f"  Max stack size: {func.max_stack_size}")
    print(f"  Number of instructions: {len(func.instructions)}")
    print(f"  Number of constants: {len(func.constants)}")
    print(f"  Number of functions: {len(func.functions)}")
    print(f"  Number of locals: {len(func.locals)}")
    print()


def print_constants(function):
    """Print constants table"""
    if not function.constants:
        return
    
    print("Constants:")
    for i, constant in enumerate(function.constants):
        print(f"  {i}: {constant}")
    print()


def print_instructions(function):
    """Print decoded instructions"""
    if not function.instructions:
        return
    
    print("Instructions:")
    decoder = InstructionDecoder(function)
    decoded_instructions = decoder.decode_all_instructions()
    
    for instruction in decoded_instructions:
        print(f"  {instruction}")
    print()


def print_locals(function):
    """Print local variables"""
    if not function.locals:
        return
    
    print("Local variables:")
    for local_var in function.locals:
        print(f"  {local_var.name}: {local_var.start_pc} - {local_var.end_pc}")
    print()


def decompile_file(file_path: str, args):
    """Decompile a Lua bytecode file"""
    try:
        # Suppress debug output when writing to file
        import sys
        original_stderr = sys.stderr
        if args.output and not args.debug:
            sys.stderr = open('/dev/null', 'w')

        # Parse the bytecode file
        chunk = parse_lua_bytecode(file_path)

        # Restore stderr
        if args.output and not args.debug:
            sys.stderr.close()
            sys.stderr = original_stderr

        # If output file is specified, only output clean Lua code
        if args.output:
            lua_code = simple_decompile(chunk, clean_output=True)
            # Clean up the output - remove escape sequences
            clean_code = lua_code.replace('\\n', '\n')

            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(clean_code)
            print(f"Decompiled code written to {args.output}")
            return 0

        # Normal console output
        if args.info:
            print_chunk_info(chunk)

        if args.constants:
            print_constants(chunk.main_function)

        if args.instructions:
            print_instructions(chunk.main_function)

        if args.locals:
            print_locals(chunk.main_function)

        if args.decompile:
            # Decompile to Lua source code
            print("Decompiled Lua code:")
            print("=" * 50)
            lua_code = simple_decompile(chunk)
            print(lua_code)
            print("=" * 50)

        # If no specific options, show basic info and decompile
        if not any([args.info, args.constants, args.instructions, args.locals, args.decompile]):
            print_chunk_info(chunk)

            # Decompile by default
            print("Decompiled Lua code:")
            print("=" * 50)
            lua_code = simple_decompile(chunk)
            print(lua_code)
            print("=" * 50)

    except Exception as e:
        print(f"Error decompiling {file_path}: {e}", file=sys.stderr)
        if args.debug:
            traceback.print_exc()
        return 1

    return 0


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Python Lua Bytecode Decompiler",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m lua_decompile main.luac                    # Decompile to Lua source
  python -m lua_decompile --info main.luac             # Show file information
  python -m lua_decompile --instructions main.luac     # Show decoded instructions
  python -m lua_decompile --constants main.luac        # Show constants table
  python -m lua_decompile --locals main.luac           # Show local variables
        """
    )
    
    parser.add_argument("file", help="Lua bytecode file to decompile")
    
    parser.add_argument("--info", action="store_true",
                       help="Show file and function information")
    
    parser.add_argument("--constants", action="store_true",
                       help="Show constants table")
    
    parser.add_argument("--instructions", action="store_true",
                       help="Show decoded instructions")
    
    parser.add_argument("--locals", action="store_true",
                       help="Show local variables")
    
    parser.add_argument("--decompile", action="store_true",
                       help="Decompile to Lua source code")
    
    parser.add_argument("--debug", action="store_true",
                       help="Show debug information on errors")

    parser.add_argument("--output", "-o",
                       help="Output file for decompiled code")
    
    parser.add_argument("--version", action="version",
                       version="Python Lua Decompiler 1.0.0")
    
    args = parser.parse_args()
    
    # Check if file exists
    if not Path(args.file).exists():
        print(f"Error: File '{args.file}' not found", file=sys.stderr)
        return 1
    
    return decompile_file(args.file, args)


if __name__ == "__main__":
    sys.exit(main())
