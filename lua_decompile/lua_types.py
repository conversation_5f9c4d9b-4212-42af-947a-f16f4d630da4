"""
Data types for Lua bytecode structures
"""

from typing import List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum


class LuaVersion(Enum):
    LUA_50 = 0x50
    LUA_51 = 0x51
    LUA_52 = 0x52
    LUA_53 = 0x53


@dataclass
class LuaValue:
    """Base class for Lua values"""
    type: int
    value: Any


@dataclass
class LuaString(LuaValue):
    """Lua string value"""
    def __init__(self, value: str):
        super().__init__(4, value)  # LUA_TSTRING = 4
    
    def __str__(self):
        return f'"{self.value}"'


@dataclass
class LuaNumber(LuaValue):
    """Lua number value"""
    def __init__(self, value: Union[int, float]):
        super().__init__(3, value)  # LUA_TNUMBER = 3
    
    def __str__(self):
        return str(self.value)


@dataclass
class LuaBoolean(LuaValue):
    """Lua boolean value"""
    def __init__(self, value: bool):
        super().__init__(1, value)  # LUA_TBOOLEAN = 1
    
    def __str__(self):
        return "true" if self.value else "false"


@dataclass
class LuaNil(LuaValue):
    """Lua nil value"""
    def __init__(self):
        super().__init__(0, None)  # LUA_TNIL = 0
    
    def __str__(self):
        return "nil"


@dataclass
class LuaLocal:
    """Local variable information"""
    name: str
    start_pc: int
    end_pc: int


@dataclass
class LuaUpvalue:
    """Upvalue information"""
    name: str
    in_stack: bool
    index: int


@dataclass
class Instruction:
    """Lua instruction"""
    opcode: int
    a: int
    b: int
    c: int
    bx: int
    sbx: int
    line: int
    
    def __init__(self, instruction: int, line: int = 0):
        self.line = line
        self.opcode = instruction & 0x3F
        self.a = (instruction >> 6) & 0xFF
        self.c = (instruction >> 14) & 0x1FF
        self.b = (instruction >> 23) & 0x1FF
        self.bx = instruction >> 14
        self.sbx = self.bx - 131071


@dataclass
class LuaFunction:
    """Lua function structure"""
    source: str
    line_defined: int
    last_line_defined: int
    num_upvalues: int
    num_params: int
    is_vararg: bool
    max_stack_size: int
    
    # Code and data
    instructions: List[Instruction]
    constants: List[LuaValue]
    functions: List['LuaFunction']
    locals: List[LuaLocal]
    upvalues: List[LuaUpvalue]
    
    # Debug info
    line_info: List[int]
    
    def __init__(self):
        self.source = ""
        self.line_defined = 0
        self.last_line_defined = 0
        self.num_upvalues = 0
        self.num_params = 0
        self.is_vararg = False
        self.max_stack_size = 0
        self.instructions = []
        self.constants = []
        self.functions = []
        self.locals = []
        self.upvalues = []
        self.line_info = []


@dataclass
class LuaHeader:
    """Lua bytecode file header"""
    version: LuaVersion
    format: int
    endianness: int
    int_size: int
    size_t_size: int
    instruction_size: int
    lua_number_size: int
    integral_flag: int
    
    def __init__(self):
        self.version = LuaVersion.LUA_51
        self.format = 0
        self.endianness = 1
        self.int_size = 4
        self.size_t_size = 4
        self.instruction_size = 4
        self.lua_number_size = 8
        self.integral_flag = 0


@dataclass
class LuaChunk:
    """Complete Lua chunk (compiled file)"""
    header: LuaHeader
    main_function: LuaFunction
    
    def __init__(self):
        self.header = LuaHeader()
        self.main_function = LuaFunction()
