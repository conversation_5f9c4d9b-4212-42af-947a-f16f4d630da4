"""
Simple Lua decompiler that focuses on generating readable code
"""

from typing import List, Dict, Optional
from .lua_types import <PERSON><PERSON><PERSON>un<PERSON>, LuaChunk, Instruction
from .constants import OpCode


class SimpleDecompiler:
    """Simple decompiler that generates readable Lua code"""
    
    def __init__(self, chunk: LuaChunk):
        self.chunk = chunk
        self.function = chunk.main_function
        self.output_lines = []
    
    def get_constant_value(self, index: int) -> str:
        """Get constant value as string"""
        if 0 <= index < len(self.function.constants):
            constant = self.function.constants[index]
            if hasattr(constant, 'value'):
                if isinstance(constant.value, str):
                    return f'"{constant.value}"'
                else:
                    return str(constant.value)
            return str(constant)
        return f"K({index})"
    
    def get_rk_value(self, rk: int) -> str:
        """Get RK value (register or constant)"""
        if rk >= 256:
            # It's a constant
            const_index = rk - 256
            return self.get_constant_value(const_index)
        else:
            # It's a register
            return f"R{rk}"
    
    def decompile_instruction(self, inst: Instruction, line_num: int) -> Optional[str]:
        """Decompile a single instruction to Lua code"""
        opcode = inst.opcode
        a, b, c = inst.a, inst.b, inst.c
        bx, sbx = inst.bx, inst.sbx
        
        # Handle common patterns
        if opcode == OpCode.GETTABLE and line_num + 2 < len(self.function.instructions):
            # Check for require pattern: GETTABLE + LOADK + TAILCALL
            next_inst = self.function.instructions[line_num + 1]
            next2_inst = self.function.instructions[line_num + 2]
            
            if (next_inst.opcode == OpCode.LOADK and 
                next2_inst.opcode == OpCode.TAILCALL and
                next_inst.a == a + 1 and
                next2_inst.a == a):
                
                # This is a require call
                table_name = self.get_rk_value(c)
                if table_name == '"require"':
                    module_name = self.get_constant_value(next_inst.bx)
                    return f"require({module_name})"
        
        elif opcode == OpCode.LOADK:
            # Skip if this is part of a require pattern
            if (line_num > 0 and 
                self.function.instructions[line_num - 1].opcode == OpCode.GETTABLE and
                line_num + 1 < len(self.function.instructions) and
                self.function.instructions[line_num + 1].opcode == OpCode.TAILCALL):
                return None
        
        elif opcode == OpCode.TAILCALL:
            # Skip if this is part of a require pattern
            if (line_num > 1 and 
                self.function.instructions[line_num - 2].opcode == OpCode.GETTABLE and
                self.function.instructions[line_num - 1].opcode == OpCode.LOADK):
                return None
        
        elif opcode == OpCode.GETTABLE:
            # Simple table access
            table = self.get_rk_value(b) if b < 256 else "R0"
            key = self.get_rk_value(c)
            return f"-- R{a} = {table}[{key}]"
        
        elif opcode == OpCode.LOADK:
            # Load constant
            value = self.get_constant_value(bx)
            return f"-- R{a} = {value}"
        
        elif opcode == OpCode.CALL:
            # Function call
            func = f"R{a}"
            args = []
            if b > 1:
                for i in range(1, b):
                    args.append(f"R{a + i}")
            args_str = ", ".join(args)
            return f"-- {func}({args_str})"
        
        elif opcode == OpCode.SETUPVAL:
            # Set upvalue
            return f"-- upvalue_{b} = R{a}"
        
        elif opcode == OpCode.VARARG:
            # Vararg
            return f"-- R{a}, ... = ..."
        
        elif opcode == OpCode.FORLOOP:
            # For loop
            return f"-- for loop (R{a})"
        
        elif opcode == OpCode.RETURN:
            # Return statement
            if b == 1:
                return "-- return"
            elif b == 2:
                return f"-- return R{a}"
            else:
                values = [f"R{a + i}" for i in range(b - 1)]
                return f"-- return {', '.join(values)}"
        
        # Default: show the raw instruction
        return f"-- {OpCode.__dict__.get(opcode, f'OP_{opcode}')} {a} {b} {c}"
    
    def analyze_patterns(self) -> List[str]:
        """Analyze instruction patterns and generate high-level Lua code"""
        statements = []
        i = 0
        
        while i < len(self.function.instructions):
            inst = self.function.instructions[i]
            
            # Look for require patterns
            if (i + 2 < len(self.function.instructions) and
                inst.opcode == OpCode.GETTABLE and
                self.function.instructions[i + 1].opcode == OpCode.LOADK and
                self.function.instructions[i + 2].opcode == OpCode.TAILCALL):
                
                # This is a require call
                key = self.get_rk_value(inst.c)
                if key == '"require"':
                    module_name = self.get_constant_value(self.function.instructions[i + 1].bx)
                    statements.append(f"require({module_name})")
                    i += 3  # Skip the next two instructions
                    continue
            
            # Look for simple assignments
            if inst.opcode == OpCode.SETUPVAL:
                statements.append(f"upvalue_{inst.b} = R{inst.a}")
            
            # Look for function calls
            elif inst.opcode == OpCode.CALL:
                func_name = f"R{inst.a}"
                args = []
                if inst.b > 1:
                    for j in range(1, inst.b):
                        args.append(f"R{inst.a + j}")
                args_str = ", ".join(args)
                statements.append(f"{func_name}({args_str})")
            
            # Look for returns
            elif inst.opcode == OpCode.RETURN:
                if inst.b == 1:
                    statements.append("return")
                elif inst.b == 2:
                    statements.append(f"return R{inst.a}")
                else:
                    values = [f"R{inst.a + j}" for j in range(inst.b - 1)]
                    statements.append(f"return {', '.join(values)}")
            
            i += 1
        
        return statements
    
    def decompile(self, clean_output=False) -> str:
        """Decompile the function to Lua code"""
        lines = []

        # Analyze patterns first
        statements = self.analyze_patterns()

        if statements and not clean_output:
            lines.append("-- High-level analysis:")
            for stmt in statements:
                lines.append(f"  {stmt}")
            lines.append("")

        # Add function signature
        if clean_output:
            # For clean output, create a proper main function
            lines.append("-- Main function that loads all required modules")
            lines.append("local function main()")
        else:
            if self.function.num_params > 0:
                params = [f"arg{i}" for i in range(self.function.num_params)]
                lines.append(f"function({', '.join(params)})")
            else:
                lines.append("function()")

        # Add a simplified version based on constants
        if not clean_output:
            lines.append("  -- This appears to be a script that loads multiple modules:")

        # Extract module names from constants
        modules = []
        for constant in self.function.constants:
            if hasattr(constant, 'value') and isinstance(constant.value, str):
                value = constant.value
                # Skip common non-module strings
                if (value not in ['require', 'init', 'start', 'math', 'userPath', '/log/wmo.log'] and
                    not value.startswith('UI_') and
                    len(value) > 1):
                    modules.append(value)

        # Generate require statements
        for module in modules[:20]:  # Limit to first 20 to avoid clutter
            lines.append(f'  require("{module}")')

        if len(modules) > 20:
            if clean_output:
                # Add all remaining modules in clean output
                for module in modules[20:]:
                    lines.append(f'  require("{module}")')
            else:
                lines.append(f"  -- ... and {len(modules) - 20} more modules")

        lines.append("end")

        # Add nested functions if they exist
        if hasattr(self.function, 'functions') and self.function.functions:
            lines.append("")
            lines.append("-- Nested functions:")
            for i, nested_func in enumerate(self.function.functions):
                if len(nested_func.instructions) > 0:
                    lines.append(f"")
                    lines.append(f"-- Nested function {i+1} ({len(nested_func.instructions)} instructions, {len(nested_func.constants)} constants)")

                    # Analyze nested function constants for clues
                    nested_modules = []
                    nested_strings = []
                    for const in nested_func.constants:
                        if hasattr(const, 'value') and isinstance(const.value, str):
                            value = const.value
                            if len(value) > 2 and value not in ['init', 'start', 'math']:
                                if any(c.isalpha() for c in value):  # Contains letters
                                    nested_strings.append(value)

                    if nested_strings:
                        lines.append(f"local function nested_function_{i+1}()")
                        lines.append(f"  -- This function contains logic related to:")
                        for s in nested_strings[:10]:  # Show first 10 strings
                            lines.append(f"  -- '{s}'")
                        if len(nested_strings) > 10:
                            lines.append(f"  -- ... and {len(nested_strings) - 10} more items")

                        # Try to identify function patterns
                        if any('sleep' in s.lower() for s in nested_strings):
                            lines.append(f"  -- Contains sleep/delay functionality")
                        if any('click' in s.lower() or 'tap' in s.lower() for s in nested_strings):
                            lines.append(f"  -- Contains click/tap functionality")
                        if any('screen' in s.lower() for s in nested_strings):
                            lines.append(f"  -- Contains screen-related functionality")

                        lines.append(f"  -- TODO: Implement {len(nested_func.instructions)} instructions")
                        lines.append(f"end")

        if clean_output:
            lines.append("")
            lines.append("-- Execute the main function")
            lines.append("main()")

        return "\n".join(lines)


def simple_decompile(chunk: LuaChunk, clean_output=False) -> str:
    """Simple decompilation function"""
    decompiler = SimpleDecompiler(chunk)
    return decompiler.decompile(clean_output)
