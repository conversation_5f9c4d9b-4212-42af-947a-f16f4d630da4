"""
Lua bytecode decompiler
"""

from typing import List, Dict, Set, Optional, Tuple
from .lua_types import LuaFunction, LuaChunk, Instruction, LuaValue, LuaString, LuaNumber, LuaBoolean, LuaNil
from .constants import OpCode
from .decoder import InstructionDecoder


class Expression:
    """Base class for expressions"""
    def __str__(self):
        return "expr"


class RegisterExpression(Expression):
    """Register reference expression"""
    def __init__(self, register: int):
        self.register = register
    
    def __str__(self):
        return f"R({self.register})"


class ConstantExpression(Expression):
    """Constant value expression"""
    def __init__(self, value: LuaValue):
        self.value = value
    
    def __str__(self):
        return str(self.value)


class BinaryOpExpression(Expression):
    """Binary operation expression"""
    def __init__(self, left: Expression, operator: str, right: Expression):
        self.left = left
        self.operator = operator
        self.right = right
    
    def __str__(self):
        return f"({self.left} {self.operator} {self.right})"


class UnaryOpExpression(Expression):
    """Unary operation expression"""
    def __init__(self, operator: str, operand: Expression):
        self.operator = operator
        self.operand = operand
    
    def __str__(self):
        return f"{self.operator}{self.operand}"


class FunctionCallExpression(Expression):
    """Function call expression"""
    def __init__(self, function: Expression, args: List[Expression]):
        self.function = function
        self.args = args
    
    def __str__(self):
        args_str = ", ".join(str(arg) for arg in self.args)
        return f"{self.function}({args_str})"


class TableAccessExpression(Expression):
    """Table access expression"""
    def __init__(self, table: Expression, key: Expression):
        self.table = table
        self.key = key
    
    def __str__(self):
        return f"{self.table}[{self.key}]"


class Statement:
    """Base class for statements"""
    def __str__(self):
        return "statement"


class AssignmentStatement(Statement):
    """Assignment statement"""
    def __init__(self, targets: List[Expression], values: List[Expression]):
        self.targets = targets
        self.values = values
    
    def __str__(self):
        targets_str = ", ".join(str(target) for target in self.targets)
        values_str = ", ".join(str(value) for value in self.values)
        return f"{targets_str} = {values_str}"


class ExpressionStatement(Statement):
    """Expression statement (function call)"""
    def __init__(self, expression: Expression):
        self.expression = expression
    
    def __str__(self):
        return str(self.expression)


class ReturnStatement(Statement):
    """Return statement"""
    def __init__(self, values: List[Expression]):
        self.values = values
    
    def __str__(self):
        if not self.values:
            return "return"
        values_str = ", ".join(str(value) for value in self.values)
        return f"return {values_str}"


class IfStatement(Statement):
    """If statement"""
    def __init__(self, condition: Expression, then_block: List[Statement], else_block: Optional[List[Statement]] = None):
        self.condition = condition
        self.then_block = then_block
        self.else_block = else_block
    
    def __str__(self):
        result = f"if {self.condition} then\\n"
        for stmt in self.then_block:
            result += f"  {stmt}\\n"
        if self.else_block:
            result += "else\\n"
            for stmt in self.else_block:
                result += f"  {stmt}\\n"
        result += "end"
        return result


class WhileStatement(Statement):
    """While loop statement"""
    def __init__(self, condition: Expression, body: List[Statement]):
        self.condition = condition
        self.body = body
    
    def __str__(self):
        result = f"while {self.condition} do\\n"
        for stmt in self.body:
            result += f"  {stmt}\\n"
        result += "end"
        return result


class ForStatement(Statement):
    """Numeric for loop statement"""
    def __init__(self, var: str, start: Expression, end: Expression, step: Optional[Expression], body: List[Statement]):
        self.var = var
        self.start = start
        self.end = end
        self.step = step
        self.body = body
    
    def __str__(self):
        if self.step:
            result = f"for {self.var} = {self.start}, {self.end}, {self.step} do\\n"
        else:
            result = f"for {self.var} = {self.start}, {self.end} do\\n"
        for stmt in self.body:
            result += f"  {stmt}\\n"
        result += "end"
        return result


class LuaDecompiler:
    """Main decompiler class"""
    
    def __init__(self, chunk: LuaChunk):
        self.chunk = chunk
        self.function = chunk.main_function
        self.registers: Dict[int, Expression] = {}
        self.statements: List[Statement] = []
        self.pc = 0
    
    def get_rk_expression(self, rk: int) -> Expression:
        """Get expression for RK value (register or constant)"""
        if rk >= 256:
            # It's a constant
            const_index = rk - 256
            if 0 <= const_index < len(self.function.constants):
                return ConstantExpression(self.function.constants[const_index])
            return ConstantExpression(LuaString(f"K({const_index})"))
        else:
            # It's a register
            if rk in self.registers:
                return self.registers[rk]
            return RegisterExpression(rk)
    
    def get_register_expression(self, reg: int) -> Expression:
        """Get expression for register"""
        if reg in self.registers:
            return self.registers[reg]
        return RegisterExpression(reg)
    
    def set_register(self, reg: int, expr: Expression):
        """Set register to expression"""
        self.registers[reg] = expr
    
    def decompile_instruction(self, instruction: Instruction) -> Optional[Statement]:
        """Decompile a single instruction"""
        opcode = instruction.opcode
        a, b, c = instruction.a, instruction.b, instruction.c
        bx, sbx = instruction.bx, instruction.sbx

        if opcode == OpCode.MOVE:
            # R(A) := R(B)
            expr = self.get_register_expression(b)
            self.set_register(a, expr)
            return None  # MOVE is usually optimized away in high-level code

        elif opcode == OpCode.LOADK:
            # R(A) := K(Bx)
            if bx < len(self.function.constants):
                expr = ConstantExpression(self.function.constants[bx])
                self.set_register(a, expr)
                return None  # Will be used in subsequent instructions

        elif opcode == OpCode.LOADBOOL:
            # R(A) := (Bool)B; if (C) pc++
            expr = ConstantExpression(LuaBoolean(b != 0))
            self.set_register(a, expr)
            return None

        elif opcode == OpCode.LOADNIL:
            # R(A) := ... := R(A+B) := nil
            expr = ConstantExpression(LuaNil())
            for i in range(b + 1):
                reg = a + i
                self.set_register(reg, expr)
            return None

        elif opcode == OpCode.GETUPVAL:
            # R(A) := UpValue[B]
            expr = RegisterExpression(f"upvalue_{b}")
            self.set_register(a, expr)
            return None

        elif opcode == OpCode.GETGLOBAL:
            # R(A) := Gbl[K(Bx)]
            if bx < len(self.function.constants):
                global_name = self.function.constants[bx].value
                expr = RegisterExpression(global_name)
                self.set_register(a, expr)
                return None

        elif opcode == OpCode.GETTABLE:
            # R(A) := R(B)[RK(C)]
            table_expr = self.get_register_expression(b)
            key_expr = self.get_rk_expression(c)
            expr = TableAccessExpression(table_expr, key_expr)
            self.set_register(a, expr)
            return None

        elif opcode == OpCode.SETGLOBAL:
            # Gbl[K(Bx)] := R(A)
            if bx < len(self.function.constants):
                global_name = self.function.constants[bx].value
                value = self.get_register_expression(a)
                return AssignmentStatement([RegisterExpression(global_name)], [value])

        elif opcode == OpCode.SETUPVAL:
            # UpValue[B] := R(A)
            target = RegisterExpression(f"upvalue_{b}")
            value = self.get_register_expression(a)
            return AssignmentStatement([target], [value])

        elif opcode == OpCode.SETTABLE:
            # R(A)[RK(B)] := RK(C)
            table_expr = self.get_register_expression(a)
            key_expr = self.get_rk_expression(b)
            value_expr = self.get_rk_expression(c)
            target = TableAccessExpression(table_expr, key_expr)
            return AssignmentStatement([target], [value_expr])

        elif opcode in [OpCode.ADD, OpCode.SUB, OpCode.MUL, OpCode.DIV, OpCode.MOD, OpCode.POW]:
            # R(A) := RK(B) op RK(C)
            op_map = {
                OpCode.ADD: "+", OpCode.SUB: "-", OpCode.MUL: "*",
                OpCode.DIV: "/", OpCode.MOD: "%", OpCode.POW: "^"
            }
            left = self.get_rk_expression(b)
            right = self.get_rk_expression(c)
            expr = BinaryOpExpression(left, op_map[opcode], right)
            self.set_register(a, expr)
            return None

        elif opcode in [OpCode.UNM, OpCode.NOT, OpCode.LEN]:
            # R(A) := op R(B)
            op_map = {OpCode.UNM: "-", OpCode.NOT: "not ", OpCode.LEN: "#"}
            operand = self.get_register_expression(b)
            expr = UnaryOpExpression(op_map[opcode], operand)
            self.set_register(a, expr)
            return None

        elif opcode == OpCode.CONCAT:
            # R(A) := R(B).. ... ..R(C)
            # Simplified: just concatenate B and C
            left = self.get_register_expression(b)
            right = self.get_register_expression(c)
            expr = BinaryOpExpression(left, "..", right)
            self.set_register(a, expr)
            return None

        elif opcode == OpCode.CALL:
            # R(A), ... ,R(A+C-2) := R(A)(R(A+1), ... ,R(A+B-1))
            func_expr = self.get_register_expression(a)
            args = []
            if b > 1:
                for i in range(1, b):
                    args.append(self.get_register_expression(a + i))

            call_expr = FunctionCallExpression(func_expr, args)

            if c == 1:
                # No return values, just a call statement
                return ExpressionStatement(call_expr)
            else:
                # Has return values - store them in registers
                for i in range(c - 1 if c > 1 else 1):
                    self.set_register(a + i, call_expr)
                return None  # The assignment will be handled by subsequent instructions

        elif opcode == OpCode.TAILCALL:
            # return R(A)(R(A+1), ... ,R(A+B-1))
            func_expr = self.get_register_expression(a)
            args = []
            if b > 1:
                for i in range(1, b):
                    args.append(self.get_register_expression(a + i))

            call_expr = FunctionCallExpression(func_expr, args)
            return ReturnStatement([call_expr])

        elif opcode == OpCode.RETURN:
            # return R(A), ... ,R(A+B-2)
            values = []
            if b > 1:
                for i in range(b - 1):
                    values.append(self.get_register_expression(a + i))
            return ReturnStatement(values)

        # Add more opcodes as needed...

        return None
    
    def decompile(self) -> List[Statement]:
        """Decompile the function"""
        self.statements = []
        
        for i, instruction in enumerate(self.function.instructions):
            self.pc = i
            stmt = self.decompile_instruction(instruction)
            if stmt:
                self.statements.append(stmt)
        
        return self.statements
    
    def generate_lua_code(self) -> str:
        """Generate Lua source code"""
        lines = []
        
        # Function header
        if self.function.num_params > 0:
            params = [f"arg{i}" for i in range(self.function.num_params)]
            lines.append(f"function({', '.join(params)})")
        else:
            lines.append("function()")
        
        # Function body
        for stmt in self.statements:
            lines.append(f"  {stmt}")
        
        lines.append("end")
        
        return "\\n".join(lines)
