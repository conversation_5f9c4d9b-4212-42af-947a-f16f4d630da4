"""
Constants and enums for Lua bytecode decompilation
"""

# Lua signature
LUA_SIGNATURE = b'\x1bLua'
LUA_SIGNATURE_52 = b'\x1bLuaR'  # Lua 5.2 signature

# Lua version constants
LUA_VERSION_50 = 0x50
LUA_VERSION_51 = 0x51
LUA_VERSION_52 = 0x52
LUA_VERSION_53 = 0x53

# Lua data types
LUA_TNIL = 0
LUA_TBOOLEAN = 1
LUA_TLIGHTUSERDATA = 2
LUA_TNUMBER = 3
LUA_TSTRING = 4
LUA_TTABLE = 5
LUA_TFUNCTION = 6
LUA_TUSERDATA = 7
LUA_TTHREAD = 8

# Lua opcodes for different versions
class OpCode:
    # Common opcodes across versions
    MOVE = 0
    LOADK = 1
    LOADBOOL = 2
    LOADNIL = 3
    GETUPVAL = 4
    GETGLOBAL = 5
    GETTABLE = 6
    SETGLOBAL = 7
    SETUPVAL = 8
    SETTABLE = 9
    NEWTABLE = 10
    SELF = 11
    ADD = 12
    SUB = 13
    MUL = 14
    DIV = 15
    MOD = 16
    POW = 17
    UNM = 18
    NOT = 19
    LEN = 20
    CONCAT = 21
    JMP = 22
    EQ = 23
    LT = 24
    LE = 25
    TEST = 26
    TESTSET = 27
    CALL = 28
    TAILCALL = 29
    RETURN = 30
    FORLOOP = 31
    FORPREP = 32
    TFORLOOP = 33
    SETLIST = 34
    CLOSE = 35
    CLOSURE = 36
    VARARG = 37

# Instruction format constants
OPCODE_MASK = 0x3F
SIZE_OP = 6
SIZE_A = 8
SIZE_B = 9
SIZE_C = 9
SIZE_Bx = SIZE_B + SIZE_C
SIZE_sBx = SIZE_Bx

POS_OP = 0
POS_A = POS_OP + SIZE_OP
POS_C = POS_A + SIZE_A
POS_B = POS_C + SIZE_C
POS_Bx = POS_A + SIZE_A

MAXARG_A = (1 << SIZE_A) - 1
MAXARG_B = (1 << SIZE_B) - 1
MAXARG_C = (1 << SIZE_C) - 1
MAXARG_Bx = (1 << SIZE_Bx) - 1
MAXARG_sBx = MAXARG_Bx >> 1

# Instruction argument types
class ArgType:
    A = "A"      # 8 bit argument
    B = "B"      # 9 bit argument
    C = "C"      # 9 bit argument
    Bx = "Bx"    # 18 bit argument
    sBx = "sBx"  # signed 18 bit argument

# Instruction modes
class InstructionMode:
    iABC = 0  # A, B, C
    iABx = 1  # A, Bx
    iAsBx = 2 # A, sBx

# Opcode information
OPCODE_INFO = {
    OpCode.MOVE: ("MOVE", InstructionMode.iABC),
    OpCode.LOADK: ("LOADK", InstructionMode.iABx),
    OpCode.LOADBOOL: ("LOADBOOL", InstructionMode.iABC),
    OpCode.LOADNIL: ("LOADNIL", InstructionMode.iABC),
    OpCode.GETUPVAL: ("GETUPVAL", InstructionMode.iABC),
    OpCode.GETGLOBAL: ("GETGLOBAL", InstructionMode.iABx),
    OpCode.GETTABLE: ("GETTABLE", InstructionMode.iABC),
    OpCode.SETGLOBAL: ("SETGLOBAL", InstructionMode.iABx),
    OpCode.SETUPVAL: ("SETUPVAL", InstructionMode.iABC),
    OpCode.SETTABLE: ("SETTABLE", InstructionMode.iABC),
    OpCode.NEWTABLE: ("NEWTABLE", InstructionMode.iABC),
    OpCode.SELF: ("SELF", InstructionMode.iABC),
    OpCode.ADD: ("ADD", InstructionMode.iABC),
    OpCode.SUB: ("SUB", InstructionMode.iABC),
    OpCode.MUL: ("MUL", InstructionMode.iABC),
    OpCode.DIV: ("DIV", InstructionMode.iABC),
    OpCode.MOD: ("MOD", InstructionMode.iABC),
    OpCode.POW: ("POW", InstructionMode.iABC),
    OpCode.UNM: ("UNM", InstructionMode.iABC),
    OpCode.NOT: ("NOT", InstructionMode.iABC),
    OpCode.LEN: ("LEN", InstructionMode.iABC),
    OpCode.CONCAT: ("CONCAT", InstructionMode.iABC),
    OpCode.JMP: ("JMP", InstructionMode.iAsBx),
    OpCode.EQ: ("EQ", InstructionMode.iABC),
    OpCode.LT: ("LT", InstructionMode.iABC),
    OpCode.LE: ("LE", InstructionMode.iABC),
    OpCode.TEST: ("TEST", InstructionMode.iABC),
    OpCode.TESTSET: ("TESTSET", InstructionMode.iABC),
    OpCode.CALL: ("CALL", InstructionMode.iABC),
    OpCode.TAILCALL: ("TAILCALL", InstructionMode.iABC),
    OpCode.RETURN: ("RETURN", InstructionMode.iABC),
    OpCode.FORLOOP: ("FORLOOP", InstructionMode.iAsBx),
    OpCode.FORPREP: ("FORPREP", InstructionMode.iAsBx),
    OpCode.TFORLOOP: ("TFORLOOP", InstructionMode.iABC),
    OpCode.SETLIST: ("SETLIST", InstructionMode.iABC),
    OpCode.CLOSE: ("CLOSE", InstructionMode.iABC),
    OpCode.CLOSURE: ("CLOSURE", InstructionMode.iABx),
    OpCode.VARARG: ("VARARG", InstructionMode.iABC),
}
