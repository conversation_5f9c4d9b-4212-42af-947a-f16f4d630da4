#!/usr/bin/env python3
"""
Analyze constants in nested functions
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lua_decompile.parser import LuaBytecodeParser

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze_nested_constants.py <luac_file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        # Read the bytecode file
        with open(filename, 'rb') as f:
            file_data = f.read()
        
        # Parse the bytecode
        parser = LuaBytecodeParser(file_data)
        chunk = parser.parse()
        
        print(f"Main function has {len(chunk.main_function.functions)} nested functions")
        
        for i, func in enumerate(chunk.main_function.functions):
            if len(func.constants) > 0:
                print(f"\nNested function {i+1} constants ({len(func.constants)} total):")
                for j, const in enumerate(func.constants):
                    if hasattr(const, 'value'):
                        value = const.value
                        if isinstance(value, str) and len(value) > 0:
                            print(f"  {j:3d}: '{value}'")
                        else:
                            print(f"  {j:3d}: {value}")
                    else:
                        print(f"  {j:3d}: {const}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
