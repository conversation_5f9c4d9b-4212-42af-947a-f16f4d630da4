#!/usr/bin/env python3
"""
简单测试解析器
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_parsing():
    """测试基础解析功能"""
    try:
        from lua_decompile.parser import LuaBytecodeParser
        
        print("正在读取字节码文件...")
        with open('main.luac', 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        print(f"文件头: {data[:20].hex()}")
        
        print("正在创建解析器...")
        parser = LuaBytecodeParser(data)
        
        print("正在解析...")
        chunk = parser.parse()
        
        print("解析成功！")
        print(f"主函数指令数: {len(chunk.main_function.instructions)}")
        print(f"主函数常量数: {len(chunk.main_function.constants)}")
        print(f"嵌套函数数: {len(chunk.main_function.functions)}")
        
        return True
        
    except Exception as e:
        print(f"解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_final_decompiler():
    """测试最终反编译器"""
    try:
        print("\n正在测试最终反编译器...")
        from final_decompiler import main as decompile_main
        
        # 重定向输出到文件
        import io
        import contextlib
        
        output = io.StringIO()
        with contextlib.redirect_stdout(output):
            decompile_main()
        
        result = output.getvalue()
        print(f"反编译输出长度: {len(result)} 字符")
        print(f"输出行数: {len(result.splitlines())}")
        
        # 保存结果
        with open('test_output.lua', 'w', encoding='utf-8') as f:
            f.write(result)
        
        print("反编译结果已保存到 test_output.lua")
        return True
        
    except Exception as e:
        print(f"反编译失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== Lua 5.2 反编译工具测试 ===")
    
    # 测试基础解析
    if test_basic_parsing():
        print("✅ 基础解析测试通过")
    else:
        print("❌ 基础解析测试失败")
        return
    
    # 测试反编译
    if test_final_decompiler():
        print("✅ 反编译测试通过")
    else:
        print("❌ 反编译测试失败")

if __name__ == "__main__":
    main()
