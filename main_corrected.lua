-- filename: 
-- version: lua52
-- line: [0, 0] id: 0
require("TSLib")
require("红尘试炼")
require("核心调用库")
require("无名打码")
require("通用传送库")
require("颜色库")
require("res")
require("share")
require("Colorful")
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
require("押镖调用库")
require("PublicFunc")
require("FlightFlag")
require("MyGameData")
require("PetTreatment")
require("登录模式")
require("初出茅庐")
init(1)
require("无名打码")
require("GameCJData")
require("GameTaskData")
require("Calligraphy")
text = readFileString(userPath() .. "/log/wmo.log")
MyGetRunningAccess = function(...)
  -- line: [29, 41] id: 1
  mSleep(1)
end
wwww, hhhh = getScreenSize()
math.randomseed(getRndNum())
startTask = false
卡顿掉帧 = 1
没点角色 = ""
SmUI = function()
  -- line: [47, 635] id: 2
  local r0_2, r1_2 = getScreenSize()
  UINew(5, "【基础设置】,【坐标价格】,【战斗设置】,【特殊设置】,【必看！！】", "运行脚本", "退出脚本", "uiconfigfuben.dat", 0, 180, 1920, 1080, "255,255,250", "142,229,238", "", "tab", 1, 31, "left")
  UILabel("功能选择:", 15, "left", "0,0,5", 200, 1)
  UICombo("Gameorder1", "跑商,青龙,定时买药,摆摊卖二药,转移二药", "3", 280, 1)
  UILabel("循环上号:", 15, "left", "0,0,0", 220, 1)
  UICombo("循环上号", "单号模式,普通循环", "0", 300, 1)
  UILabel("循环数量:", 15, "left", "0,0,0", 200, 1)
  UIEdit("角色数量", "", "", 12, "left", "0,0,0", "default", 150, 0, false)
  UILabel("跑商票数:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_跑商票数", "", "999", 15, "left", "0,0,0", "default", 200, 1)
  UILabel("青龙次数:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_青龙次数", "", "999", 15, "left", "0,0,0", "default", 200, 1)
  UILabel("卡顿掉帧:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_卡顿掉帧", "1000=1秒", "", 15, "left", "0,0,0", "default", 260, 0)
  UILabel("跑商路线:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_跑商路线", "鬼区智能,地府北俱,长安长寿,比价换线", "0", 280, 1)
  UILabel("赏金选择:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_赏金选择", "无赏金下号,跑满20赏金下号,优先赏金,不跑赏金", "3", 310, 1)
  UILabel("跑商模式:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_跑商模式", "普通跑商,联动跑商,抢货模式", "3", 310, 0)
  UILabel(1, "跑商等级:", 15, "left", "0,0,0", 200, 1)
  UICombo(1, "UI_跑商等级", "80,40,60", "", 280, 1)
  UILabel(1, "购买二药:", 15, "left", "0,0,0", 200, 1)
  UICombo("UI_二药频率", "每小时买二药,每票买二药,不买二药", "3", 310, 1)
  UICheck(1, "UI_完成不下线", "完成任务不下线", "", 510, 1, "-", 1, 0)
  UICheck(1, "UI_签到", "签到", "", 350, 0, "-", 1, 1)
  UILabel("卖体间隔:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "卖体间隔time", "分钟", "180", 14, "left", "0,0,0", "default", 240, 1)
  UILabel("活力处置:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_活力处置", "换修业,烹饪,飞行符,炼药,不操作", "0", 310, 0)
  UILine("center")
  UILabel("旗帜设置", 16, "center", "0,168,233", -1)
  UILabel("长安城", 14, "center", "0,0,5", 150, 1)
  UICombo("UI_qizi_长安", "红旗,白旗,黄旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("傲来国", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_傲来", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("朱紫国", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_朱紫", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("建邺城", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_建邺", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("长寿村", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_长寿", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("北俱芦洲", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_北俱", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190, 1)
  UILine("center")
  UILabel("坐标价格", 16, "center", "0,168,233", -1)
  UILabel(2, "长安:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "刀", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_刀", "6200", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "扇子", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_扇子", "5500", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "佛珠", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_佛珠", "5800", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "香", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_香", "3800", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "蜡烛", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_蜡烛", "1800", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "出口坐标X", 14, "left", "0,0,0", 210, 1)
  UIEdit(2, "UI_出口x", "x", "", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(2, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(2, "UI_出口y", "y", "", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(2, "北俱:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "面粉", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_面粉", "3950", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "鹿茸", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_鹿茸", "8900", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "符咒", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_符咒", "6500", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "出口坐标X", 14, "left", "0,0,0", 210, 1)
  UIEdit(2, "UI_出口x", "x", "", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(2, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(2, "UI_出口y", "y", "", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(2, "地府:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "纸钱", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_纸钱", "4120", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "首饰", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_首饰", "8800", "", 12, "left", "0,0,0", "default", 195, 1)
end
打图流程 = function()
  -- line: [637, 671] id: 3
  delFile(userPath() .. "/log/hblog.log")
  if _cmp_tb_cx(Color.主界面, {10, 100}) == false then
    _print("未进入游戏,开始执行进入游戏操作！")
    _游戏.进入()
  else
    _功能.屏蔽("close")
    _print("正在游戏中")
  end
  toast("正在游戏中", 1)
  if UI_上传截图 then
    mSleep(2222)
    _记录图片()
  end
  if UI_Gameorder1 == 0 then
    跑商任务()
  elseif UI_Gameorder1 == 1 then
    青龙任务()
  elseif UI_Gameorder1 == 2 then
    定时买药任务()
  elseif UI_Gameorder1 == 3 then
    摆摊卖二药()
  elseif UI_Gameorder1 == 4 then
    转移二药总流程()
  end
  if UI_循环上号 == 1 then
    _功能.下号()
    mSleep(3000)
    _功能.上号()
  end
end
GOGAME = function()
  -- line: [673, 939] id: 4
  if UI_刀 == "" then
    UI_刀 = 6200
  end
  if UI_扇子 == "" then
    UI_扇子 = 5500
  end
  if UI_佛珠 == "" then
    UI_佛珠 = 5800
  end
  if UI_香 == "" then
    UI_香 = 3800
  end
  if UI_蜡烛 == "" then
    UI_蜡烛 = 1800
  end
  if UI_面粉 == "" then
    UI_面粉 = 3950
  end
  if UI_鹿茸 == "" then
    UI_鹿茸 = 8900
  end
  if UI_符咒 == "" then
    UI_符咒 = 6500
  end
  if UI_纸钱 == "" then
    UI_纸钱 = 4120
  end
  if UI_首饰 == "" then
    UI_首饰 = 8800
  end
  if UI_珍珠 == "" then
    UI_珍珠 = 7500
  end
  if UI_夜明珠 == "" then
    UI_夜明珠 = 9200
  end
  if UI_金创药 == "" then
    UI_金创药 = 640
  end
  if UI_酒 == "" then
    UI_酒 = 1200
  end
  if UI_棉布 == "" then
    UI_棉布 = 3000
  end
  if UI_盐 == "" then
    UI_盐 = 1000
  end
  if UI_铁 == "" then
    UI_铁 = 2000
  end
  if UI_木材 == "" then
    UI_木材 = 3000
  end
  if UI_人参 == "" then
    UI_人参 = 7500
  end
  if UI_铜 == "" then
    UI_铜 = 2000
  end
  if UI_帽子 == "" then
    UI_帽子 = 3000
  end
  if UI_武器 == "" then
    UI_武器 = 4000
  end
  if UI_靴子 == "" then
    UI_靴子 = 2000
  end
  if UI_腰带 == "" then
    UI_腰带 = 1200
  end
  if UI_卡顿掉帧 == "" then
    UI_卡顿掉帧 = 1000
  end
  if UI_跑商票数 == "" then
    UI_跑商票数 = 999
  end
  if UI_青龙次数 == "" then
    UI_青龙次数 = 999
  end
  if 卖体间隔time == "" then
    卖体间隔time = 180
  end
  卡顿掉帧 = tonumber(UI_卡顿掉帧)
  跑商票数 = tonumber(UI_跑商票数)
  青龙次数 = tonumber(UI_青龙次数)
  卖体间隔 = tonumber(卖体间隔time)
  打图流程()
end
start = function()
  -- line: [941, 1274] id: 5
  SmUI()
  if UI_上传截图 then
    mSleep(2222)
    _记录图片()
  end
  if UI_循环上号 == 0 then
    GOGAME()
  else
    repeat
      GOGAME()
      if UI_循环上号 == 1 then
        _功能.下号()
        mSleep(3000)
        _功能.上号()
      end
    until false
  end
end
押镖任务 = function()
  -- line: [1276, 1308] id: 6
  _功能.屏蔽("close")
  _print("开始押镖任务")
  _押镖.开始()
  repeat
    mSleep(1000)
  until _押镖.是否完成() == true
  _print("押镖任务完成")
end
卖体转钱任务 = function()
  -- line: [1310, 1314] id: 7
end
跑商任务 = function()
  -- line: [1316, 1327] id: 8
  _功能.屏蔽("close")
  _print("开始跑商任务")
  _跑商.开始()
  repeat
    mSleep(1000)
  until _跑商.是否完成() == true
  _print("跑商任务完成")
end
青龙任务 = function()
  -- line: [1329, 1337] id: 9
  _功能.屏蔽("close")
  _print("开始青龙任务")
  _青龙.开始()
  _print("青龙任务完成")
end
定时买药任务 = function()
  -- line: [1339, 1364] id: 10
  _功能.屏蔽("close")
  _print("开始定时买药任务")
  repeat
    _买药.执行()
    mSleep(60000)
  until false
end
摆摊卖二药 = function()
  -- line: [1366, 1393] id: 11
  _功能.屏蔽("close")
  _print("开始摆摊卖二药")
  _摆摊.设置("二药")
  _摆摊.开始()
  repeat
    mSleep(1000)
  until _摆摊.是否完成() == true
  _print("摆摊卖二药完成")
end
转移二药总流程 = function()
  -- line: [1395, 1416] id: 12
  _功能.屏蔽("close")
  _print("开始转移二药")
  _转移.设置("二药")
  _转移.开始()
  repeat
    mSleep(1000)
  until _转移.是否完成() == true
  _print("转移二药完成")
end
买图转移 = function()
  -- line: [1418, 1431] id: 13
  _功能.屏蔽("close")
  _print("开始买图转移")
  _买图.开始()
  _转移.开始()
  _print("买图转移完成")
end
start()
