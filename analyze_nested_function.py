#!/usr/bin/env python3
"""
Analyze the nested function with 1865 instructions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from lua_decompile.parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def analyze_nested_function(filename):
    """Analyze the nested function content"""
    parser = LuaBytecodeParser(filename)
    chunk = parser.parse()
    
    print(f"Main function has {len(chunk.main_function.functions)} nested functions")
    
    for i, func in enumerate(chunk.main_function.functions):
        print(f"\nNested function {i+1}:")
        print(f"  Instructions: {len(func.instructions)}")
        print(f"  Constants: {len(func.constants)}")
        print(f"  Source: '{func.source}'")
        print(f"  Lines: {func.line_defined}-{func.last_line_defined}")
        print(f"  Params: {func.num_params}, Upvalues: {func.num_upvalues}")
        
        if len(func.constants) > 0:
            print(f"  First 10 constants:")
            for j, const in enumerate(func.constants[:10]):
                if hasattr(const, 'value'):
                    print(f"    {j}: {const.value}")
                else:
                    print(f"    {j}: {const}")
        
        if len(func.instructions) > 0:
            print(f"  First 5 instructions:")
            for j, inst in enumerate(func.instructions[:5]):
                print(f"    {j}: opcode={inst.opcode}, A={inst.a}, B={inst.b}, C={inst.c}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze_nested_function.py <luac_file>")
        sys.exit(1)
    
    analyze_nested_function(sys.argv[1])
