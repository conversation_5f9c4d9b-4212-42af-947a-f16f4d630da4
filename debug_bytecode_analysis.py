#!/usr/bin/env python3
"""
深度分析字节码解析问题
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lua_decompile.parser import LuaBytecodeParser

def analyze_smui_function():
    """深度分析SmUI函数的字节码"""
    
    with open('main.luac', 'rb') as f:
        file_data = f.read()
    
    parser = LuaBytecodeParser(file_data)
    chunk = parser.parse()
    
    print("=== 字节码文件基本信息 ===")
    print(f"文件大小: {len(file_data)} 字节")
    print(f"主函数指令数: {len(chunk.main_function.instructions)}")
    print(f"主函数常量数: {len(chunk.main_function.constants)}")
    print(f"嵌套函数数: {len(chunk.main_function.functions)}")
    
    print("\n=== 主函数常量列表 ===")
    for i, const in enumerate(chunk.main_function.constants):
        if hasattr(const, 'value'):
            value = const.value
            if isinstance(value, str) and len(value) > 50:
                value = value[:50] + "..."
            print(f"K({i}): {type(value).__name__} = {repr(value)}")
        else:
            print(f"K({i}): {type(const).__name__} = {repr(const)}")
    
    print("\n=== 主函数指令分析 ===")
    for i, inst in enumerate(chunk.main_function.instructions):
        print(f"指令{i}: opcode={inst.opcode}, A={inst.a}, B={inst.b}, C={inst.c}")
        if hasattr(inst, 'bx'):
            print(f"        Bx={inst.bx}")
    
    # 分析SmUI函数（通常是第二个函数，索引1）
    if len(chunk.main_function.functions) > 1:
        smui_func = chunk.main_function.functions[1]
        print(f"\n=== SmUI函数详细分析 ===")
        print(f"指令数: {len(smui_func.instructions)}")
        print(f"常量数: {len(smui_func.constants)}")
        
        print(f"\n=== SmUI函数常量列表 ===")
        for i, const in enumerate(smui_func.constants):
            if hasattr(const, 'value'):
                value = const.value
                if isinstance(value, str) and len(value) > 30:
                    value = value[:30] + "..."
                print(f"K({i}): {type(value).__name__} = {repr(value)}")
            else:
                print(f"K({i}): {type(const).__name__} = {repr(const)}")
        
        print(f"\n=== SmUI函数前50条指令 ===")
        for i, inst in enumerate(smui_func.instructions[:50]):
            opcode_name = get_opcode_name(inst.opcode)
            print(f"指令{i}: {opcode_name}({inst.opcode}) A={inst.a}, B={inst.b}, C={inst.c}")
            if hasattr(inst, 'bx'):
                print(f"        Bx={inst.bx}")
        
        print(f"\n=== 分析指令模式 ===")
        analyze_instruction_patterns(smui_func)

def get_opcode_name(opcode):
    """获取操作码名称"""
    opcode_names = {
        0: "MOVE", 1: "LOADK", 2: "LOADKX", 3: "LOADBOOL", 4: "LOADNIL",
        5: "GETUPVAL", 6: "GETTABUP", 7: "GETTABLE", 8: "SETTABUP", 9: "SETUPVAL",
        10: "SETTABLE", 11: "NEWTABLE", 12: "SELF", 13: "ADD", 14: "SUB",
        15: "MUL", 16: "DIV", 17: "MOD", 18: "POW", 19: "UNM", 20: "NOT",
        21: "LEN", 22: "CONCAT", 23: "JMP", 24: "EQ", 25: "LT", 26: "LE",
        27: "TEST", 28: "TESTSET", 29: "CALL", 30: "TAILCALL", 31: "RETURN",
        32: "FORLOOP", 33: "FORPREP", 34: "TFORCALL", 35: "TFORLOOP", 36: "SETLIST",
        37: "CLOSURE", 38: "VARARG", 39: "EXTRAARG"
    }
    return opcode_names.get(opcode, f"UNKNOWN({opcode})")

def analyze_instruction_patterns(func):
    """分析指令模式"""
    instructions = func.instructions
    constants = func.constants
    
    # 统计指令类型
    opcode_count = {}
    for inst in instructions:
        opcode_name = get_opcode_name(inst.opcode)
        opcode_count[opcode_name] = opcode_count.get(opcode_name, 0) + 1
    
    print("指令类型统计:")
    for opcode, count in sorted(opcode_count.items(), key=lambda x: x[1], reverse=True):
        print(f"  {opcode}: {count}次")
    
    # 查找函数调用模式
    print(f"\n函数调用模式分析:")
    call_patterns = []
    for i, inst in enumerate(instructions):
        if inst.opcode == 29:  # CALL
            # 查找前面的LOADK指令来确定函数名
            func_name = "unknown"
            for j in range(max(0, i-10), i):
                prev_inst = instructions[j]
                if prev_inst.opcode == 1:  # LOADK
                    const_val = get_constant_value(constants, prev_inst.c)
                    if isinstance(const_val, str) and const_val.startswith("UI"):
                        func_name = const_val
                        break
            call_patterns.append((i, func_name, inst.a, inst.b, inst.c))
    
    print(f"发现 {len(call_patterns)} 个函数调用:")
    for i, (pos, name, a, b, c) in enumerate(call_patterns[:20]):  # 只显示前20个
        print(f"  调用{i}: 位置{pos}, 函数={name}, A={a}, B={b}, C={c}")

def get_constant_value(constants, index):
    """获取常量值"""
    if 0 <= index < len(constants):
        const = constants[index]
        if hasattr(const, 'value'):
            return const.value
        return const
    return f"K({index})"

def main():
    try:
        analyze_smui_function()
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
