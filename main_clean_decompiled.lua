-- filename:
-- version: lua52
-- line: [0, 0] id: 0
require("TSLib")
require("红尘试炼")
require("核心调用库")
require("无名打码")
require("通用传送库")
require("颜色库")
require("res")
require("share")
require("Colorful")
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
require("押镖调用库")
require("PublicFunc")
require("FlightFlag")
require("MyGameData")
require("PetTreatment")
require("登录模式")
require("初出茅庐")
init(1.0)
require("无名打码")
require("GameCJData")
require("GameTaskData")
require("Calligraphy")
userPath()
text = readFileString(userPath() .. "/log/wmo.log")
MyGetRunningAccess = function()
  -- line: [?, ?] id: 0
end
wwww, hhhh = getScreenSize()
math.randomseed(getRndNum())
startTask = false
卡顿掉帧 = 1.0
没点角色 = ""
SmUI = function()
  -- line: [?, ?] id: 0
end
打图流程 = function()
  -- line: [?, ?] id: 0
end
GOGAME = function()
  -- line: [?, ?] id: 0
end
start = function()
  -- line: [?, ?] id: 0
end
押镖任务 = function()
  -- line: [?, ?] id: 0
end
卖体转钱任务 = function()
  -- line: [?, ?] id: 0
end
跑商任务 = function()
  -- line: [?, ?] id: 0
end
青龙任务 = function()
  -- line: [?, ?] id: 0
end
定时买药任务 = function()
  -- line: [?, ?] id: 0
end
摆摊卖二药 = function()
  -- line: [?, ?] id: 0
end
转移二药总流程 = function()
  -- line: [?, ?] id: 0
end
买图转移 = function()
  -- line: [?, ?] id: 0
end
start()
