#!/usr/bin/env python3
"""
Debug tool to analyze Lua bytecode structure
"""

import struct
import sys

def read_int(data, pos):
    """Read a 4-byte integer"""
    if pos + 4 > len(data):
        raise EOFError("Unexpected end of file")
    value = struct.unpack('<I', data[pos:pos+4])[0]
    return value, pos + 4

def read_byte(data, pos):
    """Read a single byte"""
    if pos >= len(data):
        raise EOFError("Unexpected end of file")
    return data[pos], pos + 1

def read_string(data, pos):
    """Read a Lua string"""
    # Read string length
    length, pos = read_int(data, pos)
    if length == 0:
        return "", pos
    
    # Read string data (length includes null terminator)
    if pos + length > len(data):
        raise EOFError("Unexpected end of file")
    
    string_data = data[pos:pos+length-1]  # Exclude null terminator
    pos += length
    
    try:
        return string_data.decode('utf-8'), pos
    except UnicodeDecodeError:
        return string_data.decode('latin-1'), pos

def analyze_bytecode(filename):
    """Analyze Lua bytecode file structure"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"File size: {len(data)} bytes")
    print(f"Header: {data[:12].hex()}")
    
    pos = 0
    
    # Parse header
    signature = data[pos:pos+4]
    pos += 4
    print(f"Signature: {signature}")
    
    version = data[pos]
    pos += 1
    print(f"Version: 0x{version:02x}")
    
    format_byte = data[pos]
    pos += 1
    print(f"Format: {format_byte}")
    
    # Skip header details
    pos += 10  # Skip the rest of header
    
    print(f"Main function starts at position: {pos}")
    
    # Try to find instruction patterns
    print("\nLooking for instruction patterns...")
    
    # Instructions are 4-byte aligned
    for i in range(pos, min(pos + 200, len(data) - 4), 4):
        try:
            inst_data = struct.unpack('<I', data[i:i+4])[0]
            opcode = inst_data & 0x3F
            a = (inst_data >> 6) & 0xFF
            b = (inst_data >> 23) & 0x1FF
            c = (inst_data >> 14) & 0x1FF
            
            # Look for common opcodes
            if opcode in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]:
                print(f"Position {i}: opcode={opcode}, A={a}, B={b}, C={c}")
                if i == pos + 17:  # Found instructions at position 29 in our logs
                    print(f"*** This matches our instruction start position! ***")
                    break
        except:
            continue
    
    # Look for string constants
    print("\nLooking for string constants...")
    search_pos = 100
    while search_pos < len(data) - 8:
        try:
            length, next_pos = read_int(data, search_pos)
            if 1 <= length <= 100:  # Reasonable string length
                try:
                    string_val, _ = read_string(data, search_pos)
                    if string_val and all(32 <= ord(c) <= 126 or ord(c) > 127 for c in string_val):
                        print(f"Position {search_pos}: '{string_val}' (length={length})")
                        if string_val == "require":
                            print(f"*** Found 'require' at position {search_pos} ***")
                except:
                    pass
            search_pos += 1
        except:
            search_pos += 1
    
    print(f"\nAnalysis complete.")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_bytecode.py <luac_file>")
        sys.exit(1)
    
    analyze_bytecode(sys.argv[1])
