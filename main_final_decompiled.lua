Found instructions at position 29, count: 114
Found constants start at position 489 with count 56
Successfully parsed 56 constants
Found 13 nested functions at position 1402
Parsing nested function 1/13
  Starting nested function parse at position 1406
  Source length: 29
  Lines: 117702656-1828716544
  Params: upvalues=83, params=108, vararg=True, stack=101
  Instructions count: 196720
  Unreasonable instruction count: 196720
Failed to parse nested function 1
Parsing nested function 2/13
  Starting nested function parse at position 1455
  Source length: 0
  Lines: 4190208-16777216
  Params: upvalues=0, params=0, vararg=False, stack=0
  Instructions count: 0
  Parsed 0 instructions
  Constants count: 0
  Parsed 0 constants
  Nested functions count: 0
  Successfully parsed nested function
Successfully parsed nested function 2: 0 instructions
Parsing nested function 3/13
  Starting nested function parse at position 1483
  Source length: 0
  Lines: 12032-162560
  Params: upvalues=0, params=0, vararg=False, stack=20
  Instructions count: 1865
  Parsed 1865 instructions
  Constants count: 352
  Parsed 352 constants
  Nested functions count: 0
  Successfully parsed nested function
Successfully parsed nested function 3: 1865 instructions
Parsing nested function 4/13
  Starting nested function parse at position 16755
  Source length: 1
  Source: ''
  Lines: 0-0
  Params: upvalues=0, params=0, vararg=False, stack=0
  Instructions count: 0
  Parsed 0 instructions
  Constants count: 163072
  Unreasonable constant count: 163072
  Nested functions count: 171776
  Too many nested functions: 171776
  Successfully parsed nested function
Successfully parsed nested function 4: 0 instructions
Parsing nested function 5/13
  Starting nested function parse at position 16784
  Source length: 100663296
  Source length too large: 100663296
Failed to parse nested function 5
Parsing nested function 6/13
  Starting nested function parse at position 16788
  Source length: 78
  Lines: 458818-1075642435
  Params: upvalues=128, params=0, vararg=True, stack=128
  Instructions count: 1074167809
  Unreasonable instruction count: 1074167809
Failed to parse nested function 6
Parsing nested function 7/13
  Starting nested function parse at position 16886
  Source length: 2147942467
  Source length too large: 2147942467
Failed to parse nested function 7
Parsing nested function 8/13
  Starting nested function parse at position 16890
  Source length: 3225485379
  Source length too large: 3225485379
Failed to parse nested function 8
Parsing nested function 9/13
  Starting nested function parse at position 16894
  Source length: 1075642371
  Source length too large: 1075642371
Failed to parse nested function 9
Parsing nested function 10/13
  Starting nested function parse at position 16898
  Source length: 1074135296
  Source length too large: 1074135296
Failed to parse nested function 10
Parsing nested function 11/13
  Starting nested function parse at position 16902
  Source length: 4259906
  Source length too large: 4259906
Failed to parse nested function 11
Parsing nested function 12/13
  Starting nested function parse at position 16906
  Source length: 1075642372
  Source length too large: 1075642372
Failed to parse nested function 12
Parsing nested function 13/13
  Starting nested function parse at position 16910
  Source length: 1074135296
  Source length too large: 1074135296
Failed to parse nested function 13
-- filename:
-- version: lua52
-- line: [0, 0] id: 0
require("TSLib")
require("红尘试炼")
require("核心调用库")
require("无名打码")
require("通用传送库")
require("颜色库")
require("res")
require("share")
require("Colorful")
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
require("押镖调用库")
require("PublicFunc")
require("FlightFlag")
require("MyGameData")
require("PetTreatment")
require("登录模式")
require("初出茅庐")
init(1.0)
require("无名打码")
require("GameCJData")
require("GameTaskData")
require("Calligraphy")
userPath()
text = readFileString(userPath() .. "/log/wmo.log")
MyGetRunningAccess = function()
  -- line: [?, ?] id: 0
end
wwww, hhhh = getScreenSize()
math.randomseed(getRndNum())
startTask = false
卡顿掉帧 = 1.0
没点角色 = ""
SmUI = function()
  -- line: [?, ?] id: 0
end
打图流程 = function()
  -- line: [?, ?] id: 0
end
GOGAME = function()
  -- line: [?, ?] id: 0
end
start = function()
  -- line: [?, ?] id: 0
end
押镖任务 = function()
  -- line: [?, ?] id: 0
end
卖体转钱任务 = function()
  -- line: [?, ?] id: 0
end
跑商任务 = function()
  -- line: [?, ?] id: 0
end
青龙任务 = function()
  -- line: [?, ?] id: 0
end
定时买药任务 = function()
  -- line: [?, ?] id: 0
end
摆摊卖二药 = function()
  -- line: [?, ?] id: 0
end
转移二药总流程 = function()
  -- line: [?, ?] id: 0
end
买图转移 = function()
  -- line: [?, ?] id: 0
end
start()
