#!/usr/bin/env python3
"""
修复Lua文件中的数字格式问题
"""

import re

def fix_lua_numbers(filename):
    """修复Lua文件中的浮点数格式"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换所有的 .0 结尾的数字为整数
    # 匹配模式：数字后跟.0，但不在字符串内
    def replace_float(match):
        full_match = match.group(0)
        number = match.group(1)
        return number
    
    # 使用正则表达式替换 数字.0 为 数字
    # 但要避免在字符串内的替换
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        # 检查是否在字符串内
        in_string = False
        quote_char = None
        new_line = ""
        i = 0
        
        while i < len(line):
            char = line[i]
            
            # 处理字符串边界
            if char in ['"', "'"] and (i == 0 or line[i-1] != '\\'):
                if not in_string:
                    in_string = True
                    quote_char = char
                elif char == quote_char:
                    in_string = False
                    quote_char = None
            
            # 如果不在字符串内，检查是否是需要替换的数字
            if not in_string and char.isdigit():
                # 查找完整的数字
                j = i
                while j < len(line) and (line[j].isdigit() or line[j] == '.'):
                    j += 1
                
                number_str = line[i:j]
                if number_str.endswith('.0'):
                    # 替换为整数
                    new_line += number_str[:-2]
                else:
                    new_line += number_str
                i = j
            else:
                new_line += char
                i += 1
        
        fixed_lines.append(new_line)
    
    # 写回文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))

if __name__ == "__main__":
    fix_lua_numbers('main_with_smui.lua')
    print("Fixed number formatting in main_with_smui.lua")
