#!/usr/bin/env python3
"""
Debug parser for Lua bytecode
"""

import struct

def debug_parse_lua(filename):
    with open(filename, 'rb') as f:
        data = f.read()
    
    pos = 0
    
    def read_bytes(count):
        nonlocal pos
        if pos + count > len(data):
            raise EOFError(f"EOF at pos {pos}, need {count} bytes")
        result = data[pos:pos+count]
        pos += count
        return result
    
    def read_int():
        return struct.unpack('<I', read_bytes(4))[0]
    
    def read_byte():
        return read_bytes(1)[0]
    
    print(f"File size: {len(data)} bytes")
    print()
    
    # Parse header
    print("=== HEADER ===")
    signature = read_bytes(4)
    print(f"Signature: {signature} ({''.join(chr(b) if 32 <= b <= 126 else '.' for b in signature)})")
    
    version = read_byte()
    print(f"Version: 0x{version:02x}")
    
    format_ver = read_byte()
    print(f"Format: {format_ver}")
    
    endianness = read_byte()
    print(f"Endianness: {endianness}")
    
    int_size = read_byte()
    size_t_size = read_byte()
    inst_size = read_byte()
    number_size = read_byte()
    integral = read_byte()
    
    print(f"Sizes: int={int_size}, size_t={size_t_size}, inst={inst_size}, number={number_size}, integral={integral}")
    
    # Lua 5.2 tail
    tail = read_bytes(6)
    print(f"Tail: {tail.hex()}")
    print(f"Position after header: {pos}")
    print()
    
    # Try to parse function
    print("=== FUNCTION ===")
    print(f"Starting at position: {pos}")

    # Show next 32 bytes
    print("Next 32 bytes:")
    for i in range(min(32, len(data) - pos)):
        byte_pos = pos + i
        byte_val = data[byte_pos]
        print(f"  {byte_pos:04x}: 0x{byte_val:02x} ({byte_val:3d}) {chr(byte_val) if 32 <= byte_val <= 126 else '.'}")

    # In Lua 5.2, there might be additional data before the function
    # Let's try to find the actual function start by looking for a pattern
    print("\nLooking for function start pattern...")

    # Skip to position 27 where we see 0x01 which might be the actual start
    if pos + 9 < len(data) and data[pos + 9] == 0x01:
        print(f"Found potential function start at position {pos + 9}")
        pos = pos + 9

    # Try to read source string
    try:
        source_size = read_int()
        print(f"Source size: {source_size}")

        if source_size > 0:
            source_data = read_bytes(source_size - 1)
            null_byte = read_byte()
            source = source_data.decode('utf-8', errors='ignore')
            print(f"Source: '{source}'")
        else:
            print("Empty source")

        print(f"Position after source: {pos}")

        # Read function info
        line_defined = read_int()
        last_line_defined = read_int()
        print(f"Line defined: {line_defined}")
        print(f"Last line defined: {last_line_defined}")

        num_upvalues = read_byte()
        num_params = read_byte()
        is_vararg = read_byte()
        max_stack = read_byte()

        print(f"Upvalues: {num_upvalues}, Params: {num_params}, Vararg: {is_vararg}, Stack: {max_stack}")
        print(f"Position after function info: {pos}")

        # Read instructions
        inst_count = read_int()
        print(f"Instruction count: {inst_count}")

        if inst_count > 0 and inst_count < 10000:  # Sanity check
            print("First few instructions:")
            for i in range(min(5, inst_count)):
                inst = read_int()
                opcode = inst & 0x3F
                a = (inst >> 6) & 0xFF
                b = (inst >> 23) & 0x1FF
                c = (inst >> 14) & 0x1FF
                print(f"  {i+1}: 0x{inst:08x} -> opcode={opcode}, A={a}, B={b}, C={c}")

    except Exception as e:
        print(f"Error: {e}")
        print(f"Position when error occurred: {pos}")

if __name__ == "__main__":
    print("=== ANALYZING simple.luac ===")
    debug_parse_lua("simple.luac")
    print("\n" + "="*50 + "\n")
    print("=== ANALYZING main.luac ===")
    debug_parse_lua("main.luac")
