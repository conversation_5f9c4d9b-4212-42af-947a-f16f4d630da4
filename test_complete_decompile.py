#!/usr/bin/env python3
"""
Test complete decompilation with nested functions
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lua_decompile.parser import LuaBytecodeParser
from lua_decompile.simple_decompiler import simple_decompile

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_complete_decompile.py <luac_file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        # Read the bytecode file
        with open(filename, 'rb') as f:
            file_data = f.read()

        # Parse the bytecode
        parser = LuaBytecodeParser(file_data)
        chunk = parser.parse()
        
        # Decompile with nested function info
        decompiled_code = simple_decompile(chunk, clean_output=True)
        
        # Write to file
        output_file = "main_complete_with_nested.lua"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(decompiled_code)
        
        print(f"Complete decompiled code written to {output_file}")
        
        # Print summary
        print(f"\nSummary:")
        print(f"- Main function: {len(chunk.main_function.instructions)} instructions, {len(chunk.main_function.constants)} constants")
        print(f"- Nested functions: {len(chunk.main_function.functions)}")
        
        for i, func in enumerate(chunk.main_function.functions):
            print(f"  - Function {i+1}: {len(func.instructions)} instructions, {len(func.constants)} constants")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
