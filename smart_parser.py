#!/usr/bin/env python3
"""
Smart parser that tries to find the correct function start position
"""

import struct

def smart_parse_lua(filename):
    with open(filename, 'rb') as f:
        data = f.read()
    
    pos = 0
    
    def read_bytes(count):
        nonlocal pos
        if pos + count > len(data):
            raise EOFError(f"EOF at pos {pos}, need {count} bytes")
        result = data[pos:pos+count]
        pos += count
        return result
    
    def read_int():
        return struct.unpack('<I', read_bytes(4))[0]
    
    def read_byte():
        return read_bytes(1)[0]
    
    def reset_pos(new_pos):
        nonlocal pos
        pos = new_pos
    
    print(f"File size: {len(data)} bytes")
    
    # Parse header (we know this works)
    signature = read_bytes(4)
    version = read_byte()
    format_ver = read_byte()
    endianness = read_byte()
    int_size = read_byte()
    size_t_size = read_byte()
    inst_size = read_byte()
    number_size = read_byte()
    integral = read_byte()
    tail = read_bytes(6)
    
    print(f"Header parsed, version: 0x{version:02x}, position after header: {pos}")
    
    # Now try to find the function start
    # Look for patterns that indicate instruction sequences
    print("\\nSearching for instruction patterns...")
    
    for start_offset in range(0, 20):  # Try different offsets
        test_pos = pos + start_offset
        print(f"\\nTrying offset {start_offset} (position {test_pos}):")
        
        if test_pos + 16 > len(data):
            continue
        
        # Try to read what looks like instruction count
        reset_pos(test_pos)
        try:
            inst_count = read_int()
            print(f"  Potential instruction count: {inst_count}")
            
            if 1 <= inst_count <= 10000:  # Reasonable instruction count
                print(f"  Reasonable instruction count, checking instructions...")
                
                valid_instructions = 0
                for i in range(min(5, inst_count)):
                    if pos + 4 > len(data):
                        break
                    
                    inst = read_int()
                    opcode = inst & 0x3F
                    
                    # Check if opcode is valid (0-37 for most Lua versions)
                    if 0 <= opcode <= 37:
                        valid_instructions += 1
                        a = (inst >> 6) & 0xFF
                        b = (inst >> 23) & 0x1FF
                        c = (inst >> 14) & 0x1FF
                        print(f"    Inst {i+1}: 0x{inst:08x} -> opcode={opcode}, A={a}, B={b}, C={c}")
                    else:
                        print(f"    Inst {i+1}: 0x{inst:08x} -> INVALID opcode={opcode}")
                        break
                
                if valid_instructions >= min(3, inst_count):
                    print(f"  *** FOUND LIKELY INSTRUCTION START at offset {start_offset} ***")

                    # The instruction count we found is at test_pos
                    # So the actual function structure should start before test_pos

                    # Let's examine what's before the instruction count
                    print("  Examining data before instruction count...")

                    # Show bytes before the instruction count
                    examine_start = max(pos, test_pos - 32)
                    print(f"  Bytes from {examine_start} to {test_pos}:")
                    for i in range(examine_start, test_pos):
                        byte_val = data[i]
                        print(f"    {i:04x}: 0x{byte_val:02x} ({byte_val:3d}) {chr(byte_val) if 32 <= byte_val <= 126 else '.'}")

                    # In Lua 5.2, the function structure might be:
                    # source_size(4) + source_string + line_defined(4) + last_line_defined(4) +
                    # upvalues(1) + params(1) + vararg(1) + stack(1) + instruction_count(4)

                    # Let's try to find the source string first
                    for source_start in range(pos, test_pos - 4):
                        reset_pos(source_start)
                        try:
                            source_size = read_int()
                            if source_size == 0:
                                # Empty source string
                                print(f"    Found empty source at {source_start}")
                                # After empty source: line_defined(4) + last_line_defined(4) + upvalues(1) + params(1) + vararg(1) + stack(1)
                                expected_header_end = source_start + 4 + 4 + 4 + 1 + 1 + 1 + 1
                                if expected_header_end == test_pos:
                                    print(f"    Perfect match! Function header starts at {source_start}")
                                    return source_start, test_pos, inst_count
                            elif 1 <= source_size <= 1000:
                                # Non-empty source string
                                if source_start + 4 + source_size <= test_pos:
                                    try:
                                        source_data = data[source_start + 4:source_start + 4 + source_size - 1]
                                        source_str = source_data.decode('utf-8', errors='ignore')
                                        print(f"    Found source at {source_start}: '{source_str}' (size {source_size})")

                                        # After source: line_defined(4) + last_line_defined(4) + upvalues(1) + params(1) + vararg(1) + stack(1)
                                        expected_header_end = source_start + 4 + source_size + 4 + 4 + 1 + 1 + 1 + 1
                                        if expected_header_end == test_pos:
                                            print(f"    Perfect match! Function header starts at {source_start}")
                                            return source_start, test_pos, inst_count
                                    except:
                                        pass
                        except:
                            pass

                    # If we can't find a perfect match, return the best guess
                    print(f"  No perfect match found, using best guess")
                    return test_pos - 16, test_pos, inst_count
        except:
            pass
    
    print("\\nCould not find valid function structure")
    return None, None, None

def analyze_function_at(filename, header_pos, inst_pos, inst_count):
    """Analyze the function structure at the found positions"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    pos = header_pos
    
    def read_bytes(count):
        nonlocal pos
        if pos + count > len(data):
            raise EOFError(f"EOF at pos {pos}, need {count} bytes")
        result = data[pos:pos+count]
        pos += count
        return result
    
    def read_int():
        return struct.unpack('<I', read_bytes(4))[0]
    
    def read_byte():
        return read_bytes(1)[0]
    
    print(f"\\n=== ANALYZING FUNCTION AT POSITION {header_pos} ===")
    
    # Read function header
    line_defined = read_int()
    last_line_defined = read_int()
    upvalues = read_byte()
    params = read_byte()
    vararg = read_byte()
    stack = read_byte()
    
    print(f"Function header:")
    print(f"  Line defined: {line_defined}")
    print(f"  Last line defined: {last_line_defined}")
    print(f"  Upvalues: {upvalues}")
    print(f"  Parameters: {params}")
    print(f"  Vararg: {vararg}")
    print(f"  Max stack: {stack}")
    
    # Skip to instructions
    pos = inst_pos
    actual_inst_count = read_int()
    print(f"\\nInstructions (count: {actual_inst_count}):")
    
    for i in range(min(10, actual_inst_count)):
        inst = read_int()
        opcode = inst & 0x3F
        a = (inst >> 6) & 0xFF
        b = (inst >> 23) & 0x1FF
        c = (inst >> 14) & 0x1FF
        bx = inst >> 14
        sbx = bx - 131071
        
        print(f"  {i+1:2d}: 0x{inst:08x} -> opcode={opcode:2d}, A={a:3d}, B={b:3d}, C={c:3d}, Bx={bx:5d}, sBx={sbx:6d}")

if __name__ == "__main__":
    header_pos, inst_pos, inst_count = smart_parse_lua("main.luac")
    
    if header_pos is not None:
        analyze_function_at("main.luac", header_pos, inst_pos, inst_count)
    else:
        print("Failed to find function structure")
