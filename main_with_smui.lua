-- filename:
-- version: lua52
-- line: [0, 0] id: 0
require("TSLib")
require("红尘试炼")
require("核心调用库")
require("无名打码")
require("通用传送库")
require("颜色库")
require("res")
require("share")
require("Colorful")
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
require("押镖调用库")
require("PublicFunc")
require("FlightFlag")
require("MyGameData")
require("PetTreatment")
require("登录模式")
require("初出茅庐")
init(1)
require("无名打码")
require("GameCJData")
require("GameTaskData")
require("Calligraphy")
text = readFileString(userPath() .. "/log/wmo.log")
MyGetRunningAccess = function(...)
  -- line: [29, 41] id: 1
  mSleep(1)
end
wwww, hhhh = getScreenSize()
math.randomseed(getRndNum())
startTask = false
卡顿掉帧 = 1
没点角色 = ""
SmUI = function()
  -- line: [47, 635] id: 2
  local r0_2, r1_2 = getScreenSize()
  UINew(5, "【基础设置】,【坐标价格】,【战斗设置】,【特殊设置】,【必看！！】", "运行脚本", "退出脚本", "uiconfigfuben.dat", 0, 180, 1920, 1080, "255,255,250", "142,229,238", "", "tab", 1, 31, "left")
  UILabel("功能选择:", 15, "left", "0,0,5", 200, 1)
  UICombo("Gameorder1", "跑商,青龙,定时买药,摆摊卖二药,转移二药", "3", 280, 1)
  UILabel("循环上号:", 15, "left", "0,0,0", 220, 1)
  UICombo("循环上号", "单号模式,普通循环", "0", 300, 1)
  UILabel("循环数量:", 15, "left", "0,0,0", 200, 1)
  UIEdit("角色数量", "", "", 12, "left", "0,0,0", "default", 150, 0, false)
  UILabel("跑商票数:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_跑商票数", "", "999", 15, "left", "0,0,0", "default", 200, 1)
  UILabel("青龙次数:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_青龙次数", "", "999", 15, "left", "0,0,0", "default", 200, 1)
  UILabel("卡顿掉帧:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_卡顿掉帧", "1000=1秒", "", 15, "left", "0,0,0", "default", 260, 0)
  UILabel("跑商路线:", 15.0, "left", "0,0,5", 200.0, 1.0)
  UICombo("UI_跑商路线", "鬼区智能,地府北俱,长安长寿,比价换线", "0", 280.0, 1.0)
  UILabel("赏金选择:", 15.0, "left", "0,0,5", 200.0, 1.0)
  UICombo("UI_赏金选择", "无赏金下号,跑满20赏金下号,优先赏金,不跑赏金", "3", 310.0, 1.0)
  UILabel("跑商模式:", 15.0, "left", "0,0,5", 200.0, 1.0)
  UICombo("UI_跑商模式", "普通跑商,联动跑商,抢货模式", "3", 310.0, 0.0)
  UILabel(1.0, "跑商等级:", 15.0, "left", "0,0,0", 200.0, 1.0)
  UICombo(1.0, "UI_跑商等级", "80,40,60", "", 280.0, 1.0)
  UILabel(1.0, "购买二药:", 15.0, "left", "0,0,0", 200.0, 1.0)
  UICombo("UI_二药频率", "每小时买二药,每票买二药,不买二药", "3", 310.0, 1.0)
  UICheck(1.0, "UI_完成不下线", "完成任务不下线", "", 510.0, 1.0, "-", 1.0, 0.0)
  UICheck(1.0, "UI_签到", "签到", "", 350.0, 0.0, "-", 1.0, 1.0)
  UILabel("卖体间隔:", 15.0, "left", "0,0,5", 200.0, 1.0)
  UIEdit(1.0, "卖体间隔time", "分钟", "180", 14.0, "left", "0,0,0", "default", 240.0, 1.0)
  UILabel("活力处置:", 15.0, "left", "0,0,5", 200.0, 1.0)
  UICombo("UI_活力处置", "换修业,烹饪,飞行符,炼药,不操作", "0", 310.0, 0.0)
  UILine("center")
  UILabel("旗帜设置", 16.0, "center", "0,168,233", -1.0)
  UILabel("长安城", 14.0, "center", "0,0,5", 150.0, 1.0)
  UICombo("UI_qizi_长安", "红旗,白旗,黄旗,绿旗,蓝旗", "0", 190.0, 1.0)
  UILabel("傲来国", 14.0, "center", "0,0,2", 150.0, 1.0)
  UICombo("UI_qizi_傲来", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190.0, 1.0)
  UILabel("朱紫国", 14.0, "center", "0,0,2", 150.0, 1.0)
  UICombo("UI_qizi_朱紫", "关闭,白旗,红旗,黄旗,绿旗,蓝旗", "0", 190.0, 1.0)
  UILabel("长寿村", 14.0, "center", "0,0,2", 150.0, 1.0)
  UICombo("UI_qizi_长寿", "关闭,绿旗,红旗,白旗,黄旗,蓝旗", "0", 190.0, 1.0)
  UILabel("帮派", 14.0, "center", "0,0,2", 150.0, 1.0)
  UICombo("UI_qizi_帮派", "关闭,绿旗,红旗,白旗,黄旗,蓝旗", "0", 190.0)
  UILine("center")
  UILabel("物品补充", 16.0, "center", "0,168,233", -1.0)
  UILabel("旗子价格", 14.0, "left", "0,0,0", 180.0, 1.0)
  UIEdit("UI_goumai_qizi_price", "", "80000", 14.0, "left", "0,0,0", "default", 223.0, 1.0)
  UICheck("UI_goumai_qizi_1", "购买位置", "", 360.0, 1.0, "-", 1.0, 1.0)
  UICombo("UI_goumai_qizi_1_la", "长安城,傲来国,朱紫国,长寿村", "0", 270.0, 1.0)
  UILabel("摆摊坐标", 14.0, "right", "0,0,2", 180.0, 1.0)
  UILabel("X", 14.0, "center", "0,0,0", 25.0, 1.0)
  UIEdit("UI_goumai_qizi_1_X", "", "457", 15.0, "left", "0,0,0", "default", 190.0, 1.0)
  UILabel("Y", 14.0, "center", "0,0,0", 25.0, 1.0)
  UIEdit("UI_goumai_qizi_1_Y", "", "166", 15.0, "left", "0,0,0", "default", 190.0, 0.0)
  UILabel("上面四色常规下面蓝色", 14.0, "left", "0,0,0", 430.0, 1.0)
  UICheck("UI_goumai_qizi_2", "购买位置", "", 360.0, 1.0, "-", 1.0, 1.0)
  UICombo("UI_goumai_qizi_2_la", "长安城,傲来国,朱紫国,长寿村", "0", 270.0, 1.0)
  UILabel("摆摊坐标", 14.0, "right", "0,0,2", 180.0, 1.0)
  UILabel("X", 14.0, "center", "0,0,0", 25.0, 1.0)
  UIEdit("UI_goumai_qizi_2_X", "", "457", 15.0, "left", "0,0,0", "default", 190.0, 1.0)
  UILabel("Y", 14.0, "center", "0,0,0", 25.0, 1.0)
  UIEdit("UI_goumai_qizi_2_Y", "", "166", 15.0, "left", "0,0,0", "default", 190.0)
  UILabel("物品出售", 16.0, "center", "0,168,233", -1.0)
  UILabel("二药价格", 14.0, "left", "0,0,0", 180.0, 1.0)
  UIEdit("UI_卖二药价格", "", "1222", 14.0, "left", "0,0,0", "default", 223.0, 1.0)
  UICombo("UI_卖二药地址", "建邺城,长寿村,傲来国,长安城", "0", 270.0, 1.0)
  UILabel("摆摊坐标", 14.0, "right", "0,0,2", 180.0, 1.0)
  UILabel("X", 14.0, "center", "0,0,0", 25.0, 1.0)
  UIEdit("UI_卖药坐标X", "", "85", 15.0, "left", "0,0,0", "default", 190.0, 1.0)
  UILabel("Y", 14.0, "center", "0,0,0", 25.0, 1.0)
  UIEdit("UI_卖药坐标Y", "", "28", 15.0, "left", "0,0,0", "default", 190.0, 0.0)
  UILabel(1.0, "清空任务:", 15.0, "left", "0,0,0", 200.0, 1.0)
  UICombo(1.0, "清空任务栏啊", "是,否", "1", 250.0, 1.0)
  UILabel(1.0, "转移FF:", 15.0, "left", "0,0,0", 200.0, 1.0)
  UICombo(1.0, "UI_转移FF", "是,否", "1", 250.0, 0.0)
  UILabel(2.0, "长安:", 14.0, "left", "222,0,0", 100.0, 1.0)
  UILabel(2.0, "刀", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_刀", "6200", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "扇子", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_扇子", "5500", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "佛珠", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_佛珠", "9200", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "车夫坐标X", 14.0, "left", "0,0,0", 210.0, 1.0)
  UIEdit(2.0, "UI_车夫x", "x", "", 13.0, "left", "0,0,0", "default", 170.0, 1.0)
  UILabel(2.0, "Y", 14.0, "left", "0,0,0", 30.0, 1.0)
  UIEdit(2.0, "UI_车夫y", "y", "", 13.0, "left", "0,0,0", "default", 170.0, 0.0)
  UILabel(2.0, "长寿:", 14.0, "left", "222,0,0", 100.0, 1.0)
  UILabel(2.0, "面粉", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_面粉", "3950", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "鹿茸", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_鹿茸", "8900", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "符咒", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_符咒", "6500", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "出口坐标X", 14.0, "left", "0,0,0", 210.0, 1.0)
  UIEdit(2.0, "UI_出口x", "x", "", 13.0, "left", "0,0,0", "default", 170.0, 1.0)
  UILabel(2.0, "Y", 14.0, "left", "0,0,0", 30.0, 1.0)
  UIEdit(2.0, "UI_出口y", "y", "", 13.0, "left", "0,0,0", "default", 170.0, 0.0)
  UILabel(2.0, "地府:", 14.0, "left", "222,0,0", 100.0, 1.0)
  UILabel(2.0, "纸钱", 13.0, "left", "0,0,0", 90.0, 1.0)
  UIEdit(2.0, "UI_纸钱", "4120", "", 12.0, "left", "0,0,0", "default", 195.0, 1.0)
  UILabel(2.0, "首饰", 13.0, "left", "0,0,0", 90.0, 1.0)
  -- ... and 102 more lines
end
打图流程 = function()
  -- line: [637, 671] id: 3
  delFile(userPath() .. "/log/hblog.log")
  if _cmp_tb_cx(Color.主界面, {10, 100}) == false then
    _print("未进入游戏,开始执行进入游戏操作！")
    _游戏.进入()
  else
    _功能.屏蔽("close")
    _print("正在游戏中")
  end
  toast("正在游戏中", 1)
  if UI_DATU == "自动打图" and _打图.流程() and UI_自动卖图 == "自动卖图" then
    _卖图.自动卖图(UI_图源)
  elseif UI_自动卖图 == "自动卖图" then
    _卖图.自动卖图(UI_图源)
    return
  elseif UI_只丢图 == "自动丢图" then
    _卖图.自动丢图(UI_图源)
    return
  end
  if UI_完成后_操作 == "完成后转移宝图" then
    _卖图.自动丢图(UI_图源)
    return
  else
    return
  end
end

GOGAME = function()
  -- line: [673, 939] id: 4
  金钱不足 = false
  门派名字 = ""
  放弃豆斋 = false
  正在师门 = false
  if UI_跑商路线 == "地府北俱" or UI_跑商路线 == "比价换线" or UI_跑商路线 == "长安长寿" or UI_跑商模式 == "抢货模式" then
    北俱地府 = true
  else
    北俱地府 = false
  end
  if UI_赏金选择 == "无赏金下号" or UI_赏金选择 == "跑满20赏金下号" or UI_赏金选择 == "优先赏金" then
    UI_赏金任务 = "赏金任务"
  end

  -- Price configuration based on UI settings
  if UI_刀 ~= "" then
    刀价格 = tonumber(UI_刀)
  else
    刀价格 = 6200
  end
  if UI_扇子 ~= "" then
    扇子价格 = tonumber(UI_扇子)
  else
    扇子价格 = 5500
  end
  if UI_佛珠 ~= "" then
    佛珠价格 = tonumber(UI_佛珠)
  else
    佛珠价格 = 5800
  end
  if UI_香 ~= "" then
    香价格 = tonumber(UI_香)
  else
    香价格 = 3800
  end
  if UI_蜡烛 ~= "" then
    蜡烛价格 = tonumber(UI_蜡烛)
  else
    蜡烛价格 = 1800
  end
  if UI_面粉 ~= "" then
    面粉价格 = tonumber(UI_面粉)
  else
    面粉价格 = 3950
  end
  if UI_鹿茸 ~= "" then
    鹿茸价格 = tonumber(UI_鹿茸)
  else
    鹿茸价格 = 8900
  end
  if UI_符咒 ~= "" then
    符咒价格 = tonumber(UI_符咒)
  else
    符咒价格 = 6500
  end
  if UI_纸钱 ~= "" then
    纸钱价格 = tonumber(UI_纸钱)
  else
    纸钱价格 = 4120
  end
  if UI_首饰 ~= "" then
    首饰价格 = tonumber(UI_首饰)
  else
    首饰价格 = 8800
  end

  -- Additional game configuration
  if UI_卡顿掉帧 ~= "" then
    卡顿掉帧 = tonumber(UI_卡顿掉帧)
  else
    卡顿掉帧 = 1000
  end

  -- Game mode configuration
  if UI_Gameorder1 == "跑商" then
    跑商任务()
  elseif UI_Gameorder1 == "青龙" then
    青龙任务()
  elseif UI_Gameorder1 == "定时买药" then
    定时买药任务()
  elseif UI_Gameorder1 == "摆摊卖二药" then
    摆摊卖二药()
  elseif UI_Gameorder1 == "转移二药" then
    转移二药总流程()
  end
end

start = function()
  -- line: [941, 1274] id: 5
  SmUI()
  if UI_上传截图 then
    mSleep(2222)
    _记录图片()
  end
  if UI_Gameorder1 == "跑商" then
    if UI_循环上号 == "普通循环" then
      repeat
        GOGAME()
        if UI_完成不下线 == false then
          _登录.下号()
          mSleep(5000)
          _登录.上号()
        end
      until false
    else
      GOGAME()
    end
  elseif UI_Gameorder1 == "青龙" then
    if UI_循环上号 == "普通循环" then
      repeat
        青龙任务()
        if UI_完成不下线 == false then
          _登录.下号()
          mSleep(5000)
          _登录.上号()
        end
      until false
    else
      青龙任务()
    end
  elseif UI_Gameorder1 == "定时买药" then
    定时买药任务()
  elseif UI_Gameorder1 == "摆摊卖二药" then
    摆摊卖二药()
  elseif UI_Gameorder1 == "转移二药" then
    转移二药总流程()
  end
end

押镖任务 = function()
  -- line: [1276, 1308] id: 6
  if getTarget() == "" then
    openProps()
    if isItemExist(押镖检测) == false and isItemExist("包子") == false then
      _购买.包子()
    end
    if isItemExist(押镖检测) == false then
      _购买.押镖检测()
    end
    closeProps()
    _押镖.押镖()
  else
    _押镖.押镖()
  end
end

卖体转钱任务 = function()
  -- line: [1310, 1314] id: 7
  -- Empty function in original
end

跑商任务 = function()
  -- line: [1316, 1327] id: 8
  if UI_签到 then
    自动签到()
  end
  if UI_跑商模式 == "联动跑商" then
    _跑商.联动跑商()
  else
    _跑商.跑商()
  end
end

青龙任务 = function()
  -- line: [1329, 1337] id: 9
  当前内政已刷完 = false
  青龙_次数 = 0
  _青龙.识别任务()
end

定时买药任务 = function()
  -- line: [1339, 1364] id: 10
  if UI_定时买药 == "" then
    dialog("定时时间未填写，暂时默认当前为刷药时间", 3)
    nowTime = os.date("*t", os.time())
    nowHour = nowTime.hour
    nowMin = nowTime.min
    if nowHour < 10 then
      nowHour = "0" .. nowHour
    end
    if nowMin < 10 then
      nowMin = "0" .. nowMin
    end
    UI_定时买药 = nowHour .. ":" .. nowMin
  end
  _定时买药.定时买药()
end

摆摊卖二药 = function()
  -- line: [1366, 1393] id: 11
  取药页数 = 1
  检测提示框()
  repeat
    _通用.各地仓库(UI_卖二药地址)
    _通用.取二药()
    _通用.摆摊卖二药()
    if UI_添加好友 then
      加好友()
    end
    mSleep(tonumber(UI_摆摊间隔) * 60 * 1000)
  until false
end

转移二药总流程 = function()
  -- line: [1395, 1416] id: 12
  if UI_添加好友 then
    加好友()
  end
  取药页数 = 1
  检测提示框()
  _通用.各地仓库(UI_卖二药地址)
  _通用.取二药()
  _通用.转移二药()
end

买图转移 = function()
  -- line: [1418, 1431] id: 13
  _通用.各地仓库(UI_卖二药地址)
  加好友()
  repeat
    钱够继续买图 = false
    _通用.买图转移()
    if 钱够继续买图 == false then
      break
    end
  until false
end
start()
