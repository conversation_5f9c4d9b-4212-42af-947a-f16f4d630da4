-- Main function that loads all required modules
local function main()
  require("TSLib")
  require("红尘试炼")
  require("核心调用库")
  require("无名打码")
  require("通用传送库")
  require("颜色库")
  require("res")
  require("share")
  require("Colorful")
  require("__isnlog__")
  require("bPDOP4AkqUguZpyrAgtmTm0q")
  require("P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d")
  require("押镖调用库")
  require("PublicFunc")
  require("FlightFlag")
  require("MyGameData")
  require("PetTreatment")
  require("登录模式")
  require("初出茅庐")
  require("GameCJData")
  require("GameTaskData")
  require("Calligraphy")
  require("text")
  require("readFileString")
  require("MyGetRunningAccess")
  require("wwww")
  require("hhhh")
  require("getScreenSize")
  require("randomseed")
  require("getRndNum")
  require("startTask")
  require("卡顿掉帧")
  require("没点角色")
  require("SmUI")
  require("打图流程")
  require("GOGAME")
  require("押镖任务")
  require("卖体转钱任务")
  require("跑商任务")
  require("青龙任务")
  require("定时买药任务")
  require("摆摊卖二药")
  require("转移二药总流程")
  require("买图转移")
end

-- Execute the main function
main()