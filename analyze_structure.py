#!/usr/bin/env python3
"""
Analyze the structure after constants in Lua bytecode
"""

import struct
import sys

def read_int(data, pos):
    """Read a 4-byte integer"""
    if pos + 4 > len(data):
        raise EOFError("Unexpected end of file")
    value = struct.unpack('<I', data[pos:pos+4])[0]
    return value, pos + 4

def read_byte(data, pos):
    """Read a single byte"""
    if pos >= len(data):
        raise EOFError("Unexpected end of file")
    return data[pos], pos + 1

def read_string(data, pos):
    """Read a Lua string"""
    # Read string length
    length, pos = read_int(data, pos)
    if length == 0:
        return "", pos
    
    # Read string data (length includes null terminator)
    if pos + length > len(data):
        raise EOFError("Unexpected end of file")
    
    string_data = data[pos:pos+length-1]  # Exclude null terminator
    pos += length
    
    try:
        return string_data.decode('utf-8'), pos
    except UnicodeDecodeError:
        return string_data.decode('latin-1'), pos

def analyze_after_constants(filename):
    """Analyze what comes after the constants table"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"File size: {len(data)} bytes")
    
    # We know constants start at position 489 with count 56
    const_start = 489
    pos = const_start
    
    print(f"Constants start at position: {pos}")
    
    # Read constants count
    count, pos = read_int(data, pos)
    print(f"Constants count: {count}")
    
    # Skip through all constants
    for i in range(count):
        if pos >= len(data):
            break
        try:
            const_type = data[pos]
            pos += 1
            
            print(f"Constant {i}: type={const_type} at position {pos-1}")
            
            if const_type == 4:  # String
                str_len, pos = read_int(data, pos)
                if pos + str_len <= len(data):
                    string_data = data[pos:pos+str_len-1]  # Exclude null terminator
                    pos += str_len
                    try:
                        string_val = string_data.decode('utf-8')
                        print(f"  String: '{string_val}' (length={str_len})")
                    except:
                        print(f"  String: <binary data> (length={str_len})")
                else:
                    print(f"  String: <truncated> (length={str_len})")
                    break
            elif const_type == 3:  # Number
                if pos + 8 <= len(data):
                    number = struct.unpack('<d', data[pos:pos+8])[0]
                    pos += 8
                    print(f"  Number: {number}")
                else:
                    print(f"  Number: <truncated>")
                    break
            elif const_type == 1:  # Boolean
                if pos < len(data):
                    bool_val = data[pos]
                    pos += 1
                    print(f"  Boolean: {bool_val != 0}")
                else:
                    print(f"  Boolean: <truncated>")
                    break
            elif const_type == 0:  # Nil
                print(f"  Nil")
            else:
                print(f"  Unknown type: {const_type}")
                # Try to skip unknown type
                pos += 4
        except Exception as e:
            print(f"Error parsing constant {i}: {e}")
            break
    
    print(f"\nAfter constants, position is: {pos}")
    print(f"Remaining bytes: {len(data) - pos}")
    
    # Check what's at this position
    if pos + 4 <= len(data):
        next_value, _ = read_int(data, pos)
        print(f"Next 4-byte value: {next_value}")
        
        if next_value == 0:
            print("This is likely the nested function count (0 = no nested functions)")
        elif 1 <= next_value <= 20:
            print(f"This could be nested function count: {next_value}")
        else:
            print(f"This is unlikely to be function count: {next_value}")
    
    # Show next few bytes in hex
    if pos < len(data):
        remaining = min(50, len(data) - pos)
        hex_data = data[pos:pos+remaining].hex()
        print(f"Next {remaining} bytes (hex): {hex_data}")
        
        # Try to interpret as different data types
        print("\nTrying to interpret next data:")
        for i in range(0, min(20, remaining), 4):
            if pos + i + 4 <= len(data):
                val = struct.unpack('<I', data[pos+i:pos+i+4])[0]
                print(f"  Offset +{i}: {val} (0x{val:08x})")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze_structure.py <luac_file>")
        sys.exit(1)
    
    analyze_after_constants(sys.argv[1])
